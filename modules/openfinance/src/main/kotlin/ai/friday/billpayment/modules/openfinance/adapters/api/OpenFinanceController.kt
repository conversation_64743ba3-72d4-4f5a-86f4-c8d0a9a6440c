package ai.friday.billpayment.modules.openfinance.adapters.api

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.modules.openfinance.OpenFinance
import io.micronaut.context.annotation.Property
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.GetQueueUrlRequest
import software.amazon.awssdk.services.sqs.model.MessageAttributeValue
import software.amazon.awssdk.services.sqs.model.SendMessageRequest

@Secured(Role.Code.FRIDAY_CALLBACK)
@OpenFinance
@Controller("/open-finance")
class OpenFinanceController(
    private val amazonSQS: SqsClient,
    @param:Property(name = "sqs.openFinanceQueueName") private val openFinanceQueueName: String,
) {
    private val logger = LoggerFactory.getLogger(OpenFinanceController::class.java)

    @Post("/data")
    fun handleOpenFinanceData(@Body openFinanceRequest: OpenFinanceRequest): HttpResponse<Any> {
        logger.info("Received OpenFinance data: resourceType=${openFinanceRequest.resourceType}")

        try {
            val queueUrl = getQueueURL(openFinanceQueueName)
            val messageBody = getObjectMapper().writeValueAsString(openFinanceRequest.data)

            val sendMessageRequest = SendMessageRequest.builder()
                .queueUrl(queueUrl)
                .messageBody(messageBody)
                .messageAttributes(
                    mapOf(
                        "resourceType" to MessageAttributeValue.builder()
                            .dataType("String")
                            .stringValue(openFinanceRequest.resourceType)
                            .build(),
                    ),
                )
                .build()

            amazonSQS.sendMessage(sendMessageRequest)

            return HttpResponse.ok()
        } catch (e: Exception) {
            logger.error(Markers.append("request", openFinanceRequest), "OpenFinanceController#handleOpenFinanceData", e)
            return HttpResponse.serverError()
        }
    }

    private fun getQueueURL(queueName: String): String {
        return amazonSQS.getQueueUrl(GetQueueUrlRequest.builder().queueName(queueName).build()).queueUrl()
    }
}

data class OpenFinanceRequest(
    val resourceType: String,
    val data: Any,
)