package ai.friday.billpayment.adapters.dynamodb

import io.micronaut.context.annotation.Property
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient

private const val DYNAMODB_OPEN_FINANCE_DATA_TABLE_NAME = "dynamodb.openFinanceDataTableName"

abstract class AbstractOpenFinanceDynamoDAO<T>(
    cli: DynamoDbEnhancedClient,
    type: Class<T>,
) : AbstractDynamoDAO<T>(
    cli = cli,
    type = type,
) {
    private val logger = LoggerFactory.getLogger(AbstractOpenFinanceDynamoDAO::class.java)

    @field:Property(name = DYNAMODB_OPEN_FINANCE_DATA_TABLE_NAME)
    private var tName: String? = null

    override val tableName by lazy {
        logger.info("AbstractOpenFinanceDynamoDAO#tableName: $tName")
        tName ?: OPEN_FINANCE_DATA_TABLE_NAME
    }
}

abstract class AbstractOpenFinanceDynamoDAOAsync<T>(
    cli: DynamoDbEnhancedAsyncClient,
    type: Class<T>,
) : AbstractAsyncDynamoDAO<T>(
    cli = cli,
    type = type,
) {
    private val logger = LoggerFactory.getLogger(AbstractOpenFinanceDynamoDAOAsync::class.java)

    @field:Property(name = DYNAMODB_OPEN_FINANCE_DATA_TABLE_NAME)
    private var tName: String? = null

    override val tableName by lazy {
        logger.info("AbstractOpenFinanceDynamoDAOAsync#tableName: $tName")
        tName ?: OPEN_FINANCE_DATA_TABLE_NAME
    }
}