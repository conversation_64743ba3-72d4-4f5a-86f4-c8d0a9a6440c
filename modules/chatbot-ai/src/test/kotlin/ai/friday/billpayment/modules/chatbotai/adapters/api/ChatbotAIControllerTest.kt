package ai.friday.billpayment.modules.chatbotai.adapters.api

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.api.builders.BillTOBuilder
import ai.friday.billpayment.adapters.arbi.ArbiPixQRCodeAdapter
import ai.friday.billpayment.adapters.settlement.SettlementAdapter
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.SystemActivityType
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.InvalidBillStateChangeException
import ai.friday.billpayment.app.chatbot.ChatbotAmountQrCodeResult
import ai.friday.billpayment.app.chatbot.ChatbotQrCodeError
import ai.friday.billpayment.app.chatbot.ChatbotService
import ai.friday.billpayment.app.integrations.OFSweepingConsent
import ai.friday.billpayment.app.integrations.OFSweepingConsentPeriodicLimitUsage
import ai.friday.billpayment.app.integrations.OFSweepingConsentPeriodicUsage
import ai.friday.billpayment.app.integrations.OpenFinanceConsentService
import ai.friday.billpayment.app.integrations.OpenFinanceParticipantId
import ai.friday.billpayment.app.itp.ITPService
import ai.friday.billpayment.app.itp.ITPServiceError
import ai.friday.billpayment.app.itp.PaymentIntentId
import ai.friday.billpayment.app.manualentry.ManualEntryError
import ai.friday.billpayment.app.manualentry.ManualEntryId
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.app.notification.MessageProcessorService
import ai.friday.billpayment.app.onboarding.OnboardingTestPixService
import ai.friday.billpayment.app.pix.PixQrCode
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.chatbotai.app.ChatbotAIService
import ai.friday.billpayment.modules.chatbotai.app.ChatbotSystemActivity
import ai.friday.billpayment.modules.chatbotai.app.SystemActivityErrorReason
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.http.HttpStatus
import io.micronaut.security.authentication.Authentication
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class ChatbotAIControllerTest {

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()
    private val walletService = mockk<WalletService>() {
        every {
            findWallet(wallet.id)
        } returns wallet
        every {
            findWallets(any<AccountId>())
        } returns listOf(wallet)
    }

    private val chatbotAIService = mockk<ChatbotAIService>()
    private val qrCodeService = mockk<ArbiPixQRCodeAdapter>()
    private val chatbotService = mockk<ChatbotService>()
    private val itpService = mockk<ITPService>()
    private val paymentIntentId = PaymentIntentId()
    val authentication = mockk<Authentication>() {
        every {
            name
        } returns wallet.founder.accountId.value
    }

    val manualEntryService = mockk<ManualEntryService>()
    val messageProcessorService = mockk<MessageProcessorService>()
    val openFinanceConsentService = mockk<OpenFinanceConsentService>()
    val onboardingTestPixService = mockk<OnboardingTestPixService>()
    val settlementService = mockk<SettlementAdapter>()

    private val controller = ChatbotAIController(
        chatbotAIService = chatbotAIService,
        chatbotService = chatbotService,
        itpService = itpService,
        createBillService = mockk(),
        accountService = mockk(),
        walletService = walletService,
        pixKeyManagement = mockk(),
        contactService = mockk(),
        chatBotNotificationService = mockk(),
        subscriptionService = mockk(),
        inAppSubscriptionService = mockk(),
        manualEntryService = manualEntryService,
        accountRegisterRepository = mockk(),
        messageProcessorService = messageProcessorService,
        openFinanceConsentService = openFinanceConsentService,
        billImageProvider = mockk(relaxed = true),
        onboardingTestPixService = onboardingTestPixService,
        qrCodeService = qrCodeService,
        settlementService = settlementService,
        fichaCompensacaoService = mockk(relaxed = true),
        billTOBuilder = BillTOBuilder(pixQrCodeCompoundDataBuilder = null),
        openFinanceBankAccountService = mockk(),
    )

    private val onePixPayITPTO = OnePixPayITPTO(
        authorizationServerId = "authorizationServerId",
        bills = listOf("billId"),
    )
    val periodicLimitUsage = OFSweepingConsentPeriodicLimitUsage(
        amountLimit = 4130,
        amountUsed = 2897,
        quantityLimit = 4040,
        quantityUsed = 6927,
    )
    val periodicUsage = OFSweepingConsentPeriodicUsage(
        consentId = mockk(relaxed = true),
        daily = periodicLimitUsage,
        weekly = periodicLimitUsage,
        monthly = periodicLimitUsage,
        yearly = periodicLimitUsage,
        totalLimit = 3915,
        totalUsed = 1994,
    )

    @Nested
    @DisplayName("one-pix-pay-itp")
    inner class GetOnePixPayItp {

        @Test
        fun `deve retornar CREATED com o PaymentIntentId quando funcionar`() {
            every {
                chatbotAIService.createOnePixPayITP(any(), any(), any(), any(), any())
            } returns paymentIntentId.right()

            val response = controller.getOnePixPayItp(
                authentication,
                onePixPayITPTO,
            )

            response.status shouldBe HttpStatus.CREATED
            response.getBody(ItpTokenResponseTO::class.java).get().token shouldBe paymentIntentId.value
        }

        @Test
        fun `deve retornar BAD_REQUEST com code 4001 quando o usuário tiver saldo`() {
            every {
                chatbotAIService.createOnePixPayITP(any(), any(), any(), any(), any())
            } returns ITPServiceError.SufficientBalance().left()

            val response = controller.getOnePixPayItp(
                authentication,
                onePixPayITPTO,
            )

            response.status shouldBe HttpStatus.BAD_REQUEST
            response.getBody(ResponseTO::class.java).get().code shouldBe "4002" +
                ""
        }

        @Test
        fun `deve retornar BAD_REQUEST com code 4002 quando não encontrar`() {
            every {
                chatbotAIService.createOnePixPayITP(any(), any(), any(), any(), any())
            } returns ITPServiceError.AccountNotFound().left()

            val response = controller.getOnePixPayItp(
                authentication,
                onePixPayITPTO,
            )

            response.status shouldBe HttpStatus.BAD_REQUEST
            response.getBody(ResponseTO::class.java).get().code shouldBe "4001"
        }

        @Test
        fun `deve retornar INTERNAL_SERVER_ERROR quando falhar com uma falha não conhecida`() {
            every {
                chatbotAIService.createOnePixPayITP(any(), any(), any(), any(), any())
            } returns ITPServiceError.ITPErrorWithException.ITPProviderError(NoStackTraceException()).left()

            val response = controller.getOnePixPayItp(
                authentication,
                onePixPayITPTO,
            )

            response.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        }
    }

    @Nested
    @DisplayName("mark-as-paid")
    inner class MarkAsPAid {
        @Test
        fun `deve retornar NO_CONTENT quando todas as operações funcionarem`() {
            every {
                chatbotAIService.markAsPaid(any(), any(), any())
            } returns Unit.right()

            val response = controller.markAsPaid(
                authentication,
                CheckBillsTO(
                    bills = listOf("billId1", "billId2", "billId3"),
                ),
            )

            response.status shouldBe HttpStatus.NO_CONTENT
        }

        @Test
        fun `deve retornar BAD_REQUEST quando tentar marcar uma conta agendada`() {
            every {
                chatbotAIService.markAsPaid(any(), any(), any())
            } returns InvalidBillStateChangeException(BillId("billId3"), BillStatus.ACTIVE).left()

            val response = controller.markAsPaid(
                authentication,
                CheckBillsTO(
                    bills = listOf("billId3"),
                ),
            )

            response.status shouldBe HttpStatus.BAD_REQUEST
        }

        @Test
        fun `deve retornar INTERNAL_SERVER_ERROR quando alguma operacao falhar`() {
            every {
                chatbotAIService.markAsPaid(any(), any(), any())
            } returns NoStackTraceException().left()
            val response = controller.markAsPaid(
                authentication,
                CheckBillsTO(
                    bills = listOf("billId1", "billId2", "billId3"),
                ),
            )

            response.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        }
    }

    @Nested
    @DisplayName("pix-qr-code")
    inner class PixQRCode {
        @Test
        fun `deve retornar o código copia e cola quando consegue gerar o QRCode`() {
            val payload = PixQRCodeRequestTO(
                amount = 990,
                message = "",
            )

            every {
                chatbotService.createAmountQrCode(any(), any(), any(), any())
            } returns ChatbotAmountQrCodeResult(
                qrCode = PixQrCode(value = "pixQRcode", amount = 990),
                wallet = wallet,
            ).right()

            val response = controller.getPixQRCode(payload, authentication)

            response.status shouldBe HttpStatus.OK
            val body = response.body().shouldBeTypeOf<QrCodeResponseTO>()
            body.qrCode shouldBe "pixQRcode"

            val accountIdSlot = slot<AccountId>()
            val walletIdSlot = slot<WalletId>()
            val amountSlot = slot<Long>()

            verify {
                chatbotService.createAmountQrCode(capture(accountIdSlot), capture(walletIdSlot), capture(amountSlot), "")
            }
            accountIdSlot.captured shouldBe wallet.founder.accountId
            walletIdSlot.captured shouldBe wallet.id
            amountSlot.captured shouldBe payload.amount
        }

        @Test
        fun `deve retornar erro quando não consegue gerar o QRCode`() {
            val payload = PixQRCodeRequestTO(
                amount = 990,
                message = "",
            )

            every {
                chatbotService.createAmountQrCode(any(), any(), any(), any())
            } returns ChatbotQrCodeError.UserNotAllowed.left()

            val response = controller.getPixQRCode(payload, authentication)

            response.status shouldBe HttpStatus.BAD_REQUEST
        }
    }

    @Nested
    @DisplayName("mark-reminder-as-done")
    inner class MarkReminderAsDone {

        private val payload = MarkReminderAsDoneTO(
            reminderId = "REMINDER-ID",
        )

        @Test
        fun `deve retornar NO CONTENT quando operação for executada com sucesso`() {
            testMarkAsDone(serviceResult = Unit.right(), expectedStatus = HttpStatus.NO_CONTENT)
        }

        @Test
        fun `deve retornar INTERNAL SERVER ERROR quando houver erro ao setar status do lembrete`() {
            testMarkAsDone(serviceResult = ManualEntryError.UnknownError.left(), expectedStatus = HttpStatus.INTERNAL_SERVER_ERROR)
        }

        @Test
        fun `deve retornar NOT FOUND quando lembrete não for encontrado`() {
            testMarkAsDone(serviceResult = ManualEntryError.ManualEntryNotFound.left(), expectedStatus = HttpStatus.NOT_FOUND)
        }

        private fun testMarkAsDone(serviceResult: Either<ManualEntryError, Unit>, expectedStatus: HttpStatus) {
            every { manualEntryService.markAsPaid(any()) } returns serviceResult

            val result = controller.markReminderAsDone(authentication, payload)

            result.status shouldBe expectedStatus

            verify {
                manualEntryService.markAsPaid(ManualEntryId(payload.reminderId))
            }
        }
    }

    @Nested
    @DisplayName("system-activity")
    inner class SystemActivity {

        @Test
        fun `deve retornar a lista de system activities se existir`() {
            every { chatbotAIService.getSystemActivities(any(), any()) } returns listOf(
                ChatbotSystemActivity(
                    type = SystemActivityType.PromotedSweepingAccountOptOut,
                    value = true,
                ),
            )

            val response = controller.getSystemActivities(
                authentication,
                SystemActivityRequestTO(
                    systemActivityTypes = listOf(SystemActivityType.PromotedSweepingAccountOptOut.name, "NonExistentType"),
                ),
            )

            verify {
                chatbotAIService.getSystemActivities(any(), any())
            }

            response.status shouldBe HttpStatus.OK
            val body = response.body().shouldBeTypeOf<SystemActivityResponseTO>()
            body.systemActivities.size shouldBe 1
            body.systemActivities[0].systemActivityType shouldBe SystemActivityType.PromotedSweepingAccountOptOut.name
        }

        @Test
        fun `deve retornar uma lista vazia se não achar system activities`() {
            every { chatbotAIService.getSystemActivities(any(), any()) } returns emptyList()

            val response = controller.getSystemActivities(
                authentication,
                SystemActivityRequestTO(
                    systemActivityTypes = listOf("NonExistentType", "NonExistentType2"),
                ),
            )

            verify {
                chatbotAIService.getSystemActivities(any(), any())
            }

            response.status shouldBe HttpStatus.OK
            val body = response.body().shouldBeTypeOf<SystemActivityResponseTO>()
            body.systemActivities.size shouldBe 0
        }

        @Test
        fun `deve conseguir salvar o system activity se existir o tipo`() {
            testSetSystemActivity(serviceResult = Unit.right(), expectedStatus = HttpStatus.OK)
        }

        @Test
        fun `deve retornar BadRequest quando não existir o tipo`() {
            testSetSystemActivity(serviceResult = SystemActivityErrorReason.NotImplemented.left(), expectedStatus = HttpStatus.BAD_REQUEST)
        }

        @Test
        fun `deve retornar ServerError quando der um erro não esperado`() {
            testSetSystemActivity(serviceResult = SystemActivityErrorReason.Failure(RuntimeException("teste")).left(), expectedStatus = HttpStatus.INTERNAL_SERVER_ERROR)
        }

        private fun testSetSystemActivity(serviceResult: Either<SystemActivityErrorReason, Unit>, expectedStatus: HttpStatus) {
            every { chatbotAIService.setSystemActivityFlag(any(), any(), any()) } returns serviceResult

            val result = controller.setSystemActivityFlag(
                authentication,
                SystemActivityTO(
                    systemActivityType = "SystemActivityType",
                    value = true,
                ),
            )

            result.status shouldBe expectedStatus

            verify {
                chatbotAIService.setSystemActivityFlag(any(), any(), any())
            }
        }
    }

    @Nested
    @DisplayName("sweeping-consent")
    inner class SweepingConsentTest {

        @Test
        fun `deve retornar a lista de consentimentos vazia`() {
            every {
                openFinanceConsentService.getAuthorizedSweepingConsents(wallet.id)
            } returns emptyList()

            val response = controller.sweepingConsent(authentication, wallet.id.value)

            response.status shouldBe HttpStatus.OK

            val body = response.body().shouldBeTypeOf<GetSweepingConsentResponseTO>()
            body.hasConsent shouldBe false
            body.activeConsents.shouldBeEmpty()
        }

        @Test
        fun `deve retornar a lista de consentimentos ativos`() {
            val consents = listOf(
                OFSweepingConsent(
                    participantId = OpenFinanceParticipantId(),
                    participantName = "abc",
                    participantShortName = "abc",
                    transactionLimit = 21,
                    lastSucessfulCashIn = null,
                    periodicUsage = periodicUsage,
                ),
                OFSweepingConsent(
                    participantId = OpenFinanceParticipantId(),
                    participantName = "def",
                    participantShortName = "def",
                    transactionLimit = 435,
                    lastSucessfulCashIn = null,
                    periodicUsage = periodicUsage,
                ),
            )

            every {
                openFinanceConsentService.getAuthorizedSweepingConsents(wallet.id)
            } returns consents

            val response = controller.sweepingConsent(authentication, wallet.id.value)

            response.status shouldBe HttpStatus.OK

            val body = response.body().shouldBeTypeOf<GetSweepingConsentResponseTO>()
            body.hasConsent shouldBe true
            body.activeConsents shouldMatch consents
        }

        private infix fun List<SweepingConsentTO>.shouldMatch(consents: List<OFSweepingConsent>) {
            size shouldBe consents.size
            consents.forEach { consent ->
                with(singleOrNull { it.participantId == consent.participantId.value }) {
                    this.shouldNotBeNull()
                    this shouldMatch consent
                }
            }
        }

        private infix fun SweepingConsentTO.shouldMatch(consent: OFSweepingConsent) {
            this.transactionLimit shouldBe consent.transactionLimit
            this.participantName shouldBe consent.participantName
            this.participantId shouldBe consent.participantId.value
        }
    }

    @Nested
    @DisplayName("one-pix-pay")
    inner class OnePixPay {
        @Test
        fun `deve retornar CREATED com QR code quando funcionar`() {
            every {
                chatbotAIService.createOnePixPay(any(), any(), any(), any())
            } returns mockk<ai.friday.billpayment.app.onepixpay.OnePixPay> {
                every { qrCode } returns "sample-qr-code"
            }.right()

            val response = controller.onePixPay(
                authentication,
                OnePixPayTO(
                    bills = listOf("billId1", "billId2"),
                    useCurrentBalance = true,
                ),
            )

            response.status shouldBe HttpStatus.CREATED
            response.getBody(QrCodeResponseTO::class.java).get().qrCode shouldBe "sample-qr-code"
        }

        @Test
        fun `deve retornar INTERNAL_SERVER_ERROR quando serviço retornar erro`() {
            every {
                chatbotAIService.createOnePixPay(any(), any(), any(), any())
            } returns mockk<ai.friday.billpayment.app.onepixpay.OnePixPayErrors>().left()

            val response = controller.onePixPay(
                authentication,
                OnePixPayTO(
                    bills = listOf("billId1"),
                    useCurrentBalance = true,
                ),
            )

            response.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        }
    }

    @Nested
    @DisplayName("ignore")
    inner class Ignore {
        @Test
        fun `deve retornar NO_CONTENT quando ignorar contas com sucesso`() {
            every {
                chatbotAIService.ignore(any(), any(), any())
            } returns Unit.right()

            val response = controller.ignoreBill(
                authentication,
                CheckBillsTO(
                    bills = listOf("billId1", "billId2"),
                ),
            )

            response.status shouldBe HttpStatus.NO_CONTENT
        }

        @Test
        fun `deve retornar BAD_REQUEST quando tentar ignorar conta inválida`() {
            every {
                chatbotAIService.ignore(any(), any(), any())
            } returns InvalidBillStateChangeException(BillId("billId1"), BillStatus.ACTIVE).left()

            val response = controller.ignoreBill(
                authentication,
                CheckBillsTO(
                    bills = listOf("billId1"),
                ),
            )

            response.status shouldBe HttpStatus.BAD_REQUEST
        }
    }

    @Nested
    @DisplayName("wallet not found scenarios")
    inner class WalletNotFound {

        private val walletServiceWithEmptyWallets = mockk<WalletService>() {
            every { findWallets(any<AccountId>()) } returns emptyList()
        }

        private val controllerWithNoWallets = ChatbotAIController(
            chatbotAIService = chatbotAIService,
            chatbotService = chatbotService,
            itpService = itpService,
            createBillService = mockk(),
            accountService = mockk(),
            walletService = walletServiceWithEmptyWallets,
            pixKeyManagement = mockk(),
            contactService = mockk(),
            chatBotNotificationService = mockk(),
            subscriptionService = mockk(),
            inAppSubscriptionService = mockk(),
            manualEntryService = manualEntryService,
            accountRegisterRepository = mockk(),
            messageProcessorService = messageProcessorService,
            openFinanceConsentService = openFinanceConsentService,
            billImageProvider = mockk(relaxed = true),
            onboardingTestPixService = onboardingTestPixService,
            qrCodeService = qrCodeService,
            settlementService = settlementService,
            fichaCompensacaoService = mockk(relaxed = true),
            billTOBuilder = BillTOBuilder(pixQrCodeCompoundDataBuilder = null),
            openFinanceBankAccountService = mockk(),
        )

        @Test
        fun `one-pix-pay deve retornar BAD_REQUEST quando wallet não for encontrada`() {
            val response = controllerWithNoWallets.onePixPay(
                authentication,
                OnePixPayTO(bills = listOf("billId1"), useCurrentBalance = true),
            )

            response.status shouldBe HttpStatus.BAD_REQUEST
            response.getBody(String::class.java).get() shouldBe "Wallet not found"
        }

        @Test
        fun `validateBill deve retornar BAD_REQUEST quando wallet não for encontrada`() {
            val response = controllerWithNoWallets.arbiValidateBill(
                authentication,
                ValidateBillTO(digitableLine = "34191790010104351004791020150008291070026000000", dueDate = null),
            )

            response.status shouldBe HttpStatus.BAD_REQUEST
            response.getBody(String::class.java).get() shouldBe "Wallet not found"
        }

        @Test
        fun `schedule deve retornar BAD_REQUEST quando wallet não for encontrada`() {
            val response = controllerWithNoWallets.scheduleBill(
                authentication,
                ChatbotScheduleBillsTO(bills = listOf("billId1")),
            )

            response.status shouldBe HttpStatus.BAD_REQUEST
            response.getBody(String::class.java).get() shouldBe "Wallet not found"
        }

        @Test
        fun `markAsPaid deve retornar BAD_REQUEST quando wallet não for encontrada`() {
            val response = controllerWithNoWallets.markAsPaid(
                authentication,
                CheckBillsTO(bills = listOf("billId1")),
            )

            response.status shouldBe HttpStatus.BAD_REQUEST
            response.getBody(String::class.java).get() shouldBe "Wallet not found"
        }
    }
}