package ai.friday.billpayment.modules.chatbotai.adapters.chatbot

import ai.friday.billpayment.adapters.api.AccountTO
import ai.friday.billpayment.adapters.api.toAccountTO
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentStatus
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.LegacyAccountConfiguration
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.message.QueueMessage
import ai.friday.billpayment.app.notification.MultiChannelNotification
import ai.friday.billpayment.app.notification.NotificationTemplate
import ai.friday.billpayment.app.notification.WhatsappNotification
import ai.friday.billpayment.app.payment.checkout.RequestProtocol
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.WalletId
import io.micronaut.http.HttpResponse
import io.micronaut.http.MutableHttpRequest
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class ChatbotAdapterTest {

    private val messagePublisher = mockk<MessagePublisher>()
    private val chatBotHttpClient = mockk<ChatBotHttpClient>()

    private val account = mockk<Account>(relaxed = true) {
        every { accountId } returns AccountId("account-id")
        every { mobilePhone } returns "*************"
        every { name } returns "Test User"
        every { document } returns "***********"
        every { documentType } returns "CPF"
        every { status } returns AccountStatus.ACTIVE
        every { paymentStatus } returns AccountPaymentStatus.UpToDate
        every { configuration } returns LegacyAccountConfiguration(
            accountId = null,
            creditCardConfiguration = mockk(),
            defaultWalletId = null,
            receiveDDANotification = false,
            receiveNotification = true,
            accessToken = null,
            refreshToken = null,
            externalId = null,
            groups = emptyList(),
        )
    }

    @Nested
    @DisplayName("ao enviar uma mensagem")
    inner class SendMessage {

        @Test
        fun `quando o sendMessageProtocol é QUEUE deve chamar o messagePublisher`() {
            val chatbotConfiguration = ChatbotConfiguration(
                clientid = "client-id",
                secret = "secret",
                sendMessageProtocol = RequestProtocol.QUEUE,
            )

            val publisher = ChatbotAdapter(
                messagePublisher = messagePublisher,
                chatBotNotificationGateway = "chatBotNotificationGateway",
                chatBotWebhooksQueue = "chatBotWebhooksQueue",
                chatBotStateUpdate = "chatBotStateUpdate",
                chatBotHttpClient = chatBotHttpClient,
                chatbotConfiguration = chatbotConfiguration,
                billTOBuilder = mockk(),
            )

            every { messagePublisher.sendMessage(any<QueueMessage>()) } just runs

            publisher.publishStateUpdate(account, AccountPaymentStatus.UpToDate, AccountStatus.ACTIVE)

            verify(exactly = 1) {
                messagePublisher.sendMessage(any<QueueMessage>())
            }
            verify(exactly = 0) {
                chatBotHttpClient.send(any(), any())
            }
        }

        @Test
        fun `quando o sendMessageProtocol é HTTP deve chamar o chatBotHttpClient com tenant correto`() {
            val chatbotConfiguration = ChatbotConfiguration(
                clientid = "client-id",
                secret = "secret",
                sendMessageProtocol = RequestProtocol.HTTP,
            )

            val publisher = ChatbotAdapter(
                messagePublisher = messagePublisher,
                chatBotNotificationGateway = "chatBotNotificationGateway",
                chatBotWebhooksQueue = "chatBotWebhooksQueue",
                chatBotStateUpdate = "chatBotStateUpdate",
                chatBotHttpClient = chatBotHttpClient,
                chatbotConfiguration = chatbotConfiguration,
                billTOBuilder = mockk(),
            )

            every { chatBotHttpClient.send(any(), any()) } returns HttpResponse.ok()

            publisher.publishStateUpdate(account, AccountPaymentStatus.UpToDate, AccountStatus.ACTIVE)

            verify(exactly = 0) {
                messagePublisher.sendMessage(any<QueueMessage>())
            }
            verify(exactly = 1) {
                chatBotHttpClient.send(any(), any())
            }
        }
    }

    @Nested
    @DisplayName("ao publicar notificação WhatsApp generica")
    inner class PublishGenericWhatsappNotification {

        private fun createChatbotAdapter(): ChatbotAdapter {
            val chatbotConfiguration = ChatbotConfiguration(
                clientid = "test-client-id",
                secret = "test-secret",
                sendMessageProtocol = RequestProtocol.QUEUE,
            )

            return ChatbotAdapter(
                messagePublisher = messagePublisher,
                chatBotNotificationGateway = "chatBotNotificationGateway",
                chatBotWebhooksQueue = "chatBotWebhooksQueue",
                chatBotStateUpdate = "chatBotStateUpdate",
                chatBotHttpClient = chatBotHttpClient,
                chatbotConfiguration = chatbotConfiguration,
                billTOBuilder = mockk(),
            )
        }

        @Test
        fun `quando account é DEVELOPER deve usar GenericNotificationDetailsTO`() {
            // Given
            val developerAccount = mockk<Account>(relaxed = true) {
                every { accountId } returns AccountId("dev-account-id")
                every { mobilePhone } returns "*************"
                every { name } returns "Developer User"
                every { hasGroup(AccountGroup.DEVELOPER) } returns true
            }

            val whatsappNotification = WhatsappNotification(
                receiver = ai.friday.billpayment.app.MobilePhone("*************"),
                template = NotificationTemplate("test-template"),
                configurationKey = "test-config-key",
                parameters = listOf("param1", "param2"),
            )

            every { messagePublisher.sendMessage(any<QueueMessage>()) } just runs

            val adapter = createChatbotAdapter()

            // When
            adapter.publishGenericWhatsappNotification(developerAccount, whatsappNotification)

            // Then
            val messageSlot = slot<QueueMessage>()
            verify { messagePublisher.sendMessage(capture(messageSlot)) }

            val capturedMessage = messageSlot.captured
            val jsonContent = capturedMessage.jsonObject

            // Verifica se usa GenericNotificationDetailsTO (não mais raw content para este método)
            assertTrue(jsonContent.contains("GenericNotificationDetailsTO"))
            assertTrue(!jsonContent.contains("GenericNotificationRawContentTO"))

            // Verifica que contém o objeto notification (BillPaymentNotification)
            assertTrue(jsonContent.contains("\"notification\""))
            assertTrue(jsonContent.contains("\"template\""))
            assertTrue(jsonContent.contains("\"configurationKey\":\"test-config-key\""))
            assertTrue(jsonContent.contains("\"parameters\""))

            // Verifica que os parâmetros estão como lista, não como mapa
            assertTrue(jsonContent.contains("[\"param1\",\"param2\"]"))
        }

        @Test
        fun `quando account não é DEVELOPER deve usar GenericNotificationDetailsTO`() {
            // Given
            val regularAccount = mockk<Account>(relaxed = true) {
                every { accountId } returns AccountId("regular-account-id")
                every { mobilePhone } returns "*************"
                every { name } returns "Regular User"
                every { hasGroup(AccountGroup.DEVELOPER) } returns false
            }

            val whatsappNotification = WhatsappNotification(
                receiver = ai.friday.billpayment.app.MobilePhone("*************"),
                template = NotificationTemplate("test-template"),
                configurationKey = "test-config-key",
                parameters = listOf("param1", "param2"),
            )

            every { messagePublisher.sendMessage(any<QueueMessage>()) } just runs

            val adapter = createChatbotAdapter()

            // When
            adapter.publishGenericWhatsappNotification(regularAccount, whatsappNotification)

            // Then
            val messageSlot = slot<QueueMessage>()
            verify { messagePublisher.sendMessage(capture(messageSlot)) }

            val capturedMessage = messageSlot.captured
            val jsonContent = capturedMessage.jsonObject

            // Verifica se usa GenericNotificationDetailsTO (não é DEVELOPER)
            assertTrue(jsonContent.contains("GenericNotificationDetailsTO"))
            assertTrue(!jsonContent.contains("GenericNotificationRawContentTO"))

            // Verifica que contém o objeto notification (BillPaymentNotification)
            assertTrue(jsonContent.contains("\"notification\""))
            assertTrue(jsonContent.contains("\"template\""))
            assertTrue(jsonContent.contains("\"configurationKey\":\"test-config-key\""))
            assertTrue(jsonContent.contains("\"parameters\""))

            // Verifica que os parâmetros estão como lista, não como mapa
            assertTrue(jsonContent.contains("[\"param1\",\"param2\"]"))
        }
    }

    @Nested
    @DisplayName("ao publicar notificação multicanal")
    inner class PublishNotification {

        private fun createChatbotAdapter(): ChatbotAdapter {
            val chatbotConfiguration = ChatbotConfiguration(
                clientid = "test-client-id",
                secret = "test-secret",
                sendMessageProtocol = RequestProtocol.QUEUE,
            )

            return ChatbotAdapter(
                messagePublisher = messagePublisher,
                chatBotNotificationGateway = "chatBotNotificationGateway",
                chatBotWebhooksQueue = "chatBotWebhooksQueue",
                chatBotStateUpdate = "chatBotStateUpdate",
                chatBotHttpClient = chatBotHttpClient,
                chatbotConfiguration = chatbotConfiguration,
                billTOBuilder = mockk(),
            )
        }

        @Test
        fun `deve usar sempre GenericNotificationRawContentTO para multichannel`() {
            // Given - Evitando mock aninhado usando um mock direto
            val accountTO = AccountTO(
                id = "dev-account-id",
                fullName = "Developer User",
                document = "***********",
                documentType = "CPF",
                status = AccountStatus.ACTIVE,
                msisdn = "*************",
                paymentStatus = AccountPaymentStatus.UpToDate,
                subscriptionType = SubscriptionType.PIX,
                accountGroups = listOf(AccountGroup.DEVELOPER),
                sweepingAccount = false,
            )

            val developerAccount = mockk<Account> {
                every { accountId } returns AccountId("dev-account-id")
                every { mobilePhone } returns "*************"
                every { hasGroup(AccountGroup.DEVELOPER) } returns true
            }

            // Mocka a função de extensão diretamente
            mockkStatic("ai.friday.billpayment.adapters.api.AccountControllerKt")
            every { developerAccount.toAccountTO() } returns accountTO

            val whatsappNotification = WhatsappNotification(
                receiver = ai.friday.billpayment.app.MobilePhone("*************"),
                template = NotificationTemplate("test-template"),
                configurationKey = "test-config-key",
                parameters = listOf("param1", "param2"),
            )

            val multiChannelNotification = mockk<MultiChannelNotification> {
                every { receiver } returns developerAccount
                every { walletId } returns WalletId("wallet-id")
                every { parameters } returns mapOf("key1" to "value1", "key2" to "value2")
                every { media } returns null
                every { configurationKey } returns mockk {
                    every { name } returns "test-config-key"
                }
            }

            every { messagePublisher.sendMessage(any<QueueMessage>()) } just runs

            val adapter = ChatbotAdapter(
                messagePublisher = messagePublisher,
                chatBotNotificationGateway = "chatBotNotificationGateway",
                chatBotWebhooksQueue = "chatBotWebhooksQueue",
                chatBotStateUpdate = "chatBotStateUpdate",
                chatBotHttpClient = chatBotHttpClient,
                chatbotConfiguration = ChatbotConfiguration(
                    clientid = "test-client-id",
                    secret = "test-secret",
                    sendMessageProtocol = RequestProtocol.QUEUE,
                ),
                billTOBuilder = mockk(),
            )

            // When
            adapter.publishNotification(multiChannelNotification)

            // Then
            val messageSlot = slot<QueueMessage>()
            verify { messagePublisher.sendMessage(capture(messageSlot)) }

            val capturedMessage = messageSlot.captured
            val jsonContent = capturedMessage.jsonObject

            // Verifica se usa GenericNotificationRawDetailsTO para usuários DEVELOPER
            assertTrue(jsonContent.contains("GenericNotificationRawDetailsTO"))

            // Verifica campos específicos do GenericNotificationRawContentTO
            assertTrue(jsonContent.contains("\"receiver\":{\"msisdn\":\"*************\"}"))
            assertTrue(jsonContent.contains("\"accountId\":{\"value\":\"dev-account-id\"}"))
            assertTrue(jsonContent.contains("\"configurationKey\":\"test-config-key\""))
            assertTrue(jsonContent.contains("\"clientId\":\"test-client-id\""))

            // Verifica que contém o campo arguments (parâmetros do multiChannelNotification)
            assertTrue(jsonContent.contains("\"arguments\""))
            assertTrue(jsonContent.contains("\"key1\":\"value1\""))
            assertTrue(jsonContent.contains("\"key2\":\"value2\""))

            // Verifica que tem notificationId (UUID gerado automaticamente)
            assertTrue(jsonContent.contains("\"notificationId\""))
        }
    }

    @Nested
    @DisplayName("BasicAuthClientFilter")
    inner class BasicAuthClientFilterTest {

        @Test
        fun `deve adicionar header X-TENANT-ID e basic auth nas requisições`() {
            val chatbotConfiguration = ChatbotConfiguration(
                clientid = "test-client-id",
                secret = "test-secret",
                sendMessageProtocol = RequestProtocol.HTTP,
            )
            val tenantId = "test-tenant-id"

            val filter = BasicAuthClientFilter(chatbotConfiguration, tenantId)

            val request = mockk<MutableHttpRequest<*>>(relaxed = true)

            filter.doFilter(request)

            verify(exactly = 1) {
                request.basicAuth("test-client-id", "test-secret")
            }
            verify(exactly = 1) {
                request.header("X-TENANT-ID", "test-tenant-id")
            }
        }
    }
}