package ai.friday.billpayment.modules.chatbotai.adapters.api

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.PARTIAL_ACCOUNT
import ai.friday.billpayment.adapters.api.PixKeyRequestTO
import ai.friday.billpayment.adapters.api.RequestPixRecipientTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.chatbot.ChatbotChooseTO
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountPaymentStatus
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.ChatbotType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.SystemActivity
import ai.friday.billpayment.app.account.SystemActivityKey
import ai.friday.billpayment.app.account.SystemActivityKeyType
import ai.friday.billpayment.app.account.SystemActivityType
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.chatbot.GetAccountOrganizations
import ai.friday.billpayment.app.contact.ContactService
import ai.friday.billpayment.app.inappsubscription.InAppSubscription
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionAccessConcessionId
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionProductId
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionReason
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionService
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStatus
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStore
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.ExternalTransactionId
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.integrations.OFSweepingConsent
import ai.friday.billpayment.app.integrations.OFSweepingConsentPeriodicLimitUsage
import ai.friday.billpayment.app.integrations.OFSweepingConsentPeriodicUsage
import ai.friday.billpayment.app.integrations.OpenFinanceConsentService
import ai.friday.billpayment.app.integrations.OpenFinanceParticipantId
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.SystemActivityRepository
import ai.friday.billpayment.app.itp.ITPService
import ai.friday.billpayment.app.login.Login
import ai.friday.billpayment.app.login.LoginResult
import ai.friday.billpayment.app.login.LoginService
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.notification.BillComingDueService
import ai.friday.billpayment.app.notification.MessageProcessorService
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyDetailsResult
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.subscription.Subscription
import ai.friday.billpayment.app.subscription.SubscriptionPaymentStatus
import ai.friday.billpayment.app.subscription.SubscriptionService
import ai.friday.billpayment.app.subscription.SubscriptionStatus
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.DailyPaymentLimit
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.getPaidBill
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.EMAIL
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.modules.chatbotai.app.AssistantPaymentLimit
import ai.friday.billpayment.modules.chatbotai.app.ChatbotAIService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import java.math.BigInteger
import java.util.stream.Stream
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.Arguments.arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [ME_POUPE_ENV])
class ChatbotAIControllerIntegrationTest(embeddedServer: EmbeddedServer) {
    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildPrimaryWallet(founderAccount = ACCOUNT)

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)
    private val validPhone = "*************"
    val providerUser = ProviderUser(
        id = "123",
        emailAddress = EmailAddress(email = "$<EMAIL>"),
        providerName = ProviderName.WHATSAPP,
        username = "meu nome",
    )

    val periodicLimitUsage = OFSweepingConsentPeriodicLimitUsage(
        amountLimit = 4130,
        amountUsed = 2897,
        quantityLimit = 4040,
        quantityUsed = 6927,
    )
    val periodicUsage = OFSweepingConsentPeriodicUsage(
        consentId = mockk(relaxed = true),
        daily = periodicLimitUsage,
        weekly = periodicLimitUsage,
        monthly = periodicLimitUsage,
        yearly = periodicLimitUsage,
        totalLimit = 3915,
        totalUsed = 1994,
    )
    private val activeConsents = listOf(
        OFSweepingConsent(
            participantId = OpenFinanceParticipantId(),
            participantName = "abc",
            participantShortName = "abc",
            transactionLimit = 21,
            lastSucessfulCashIn = null,
            periodicUsage = periodicUsage,
        ),
    )

    @MockBean(ChatbotAIService::class)
    fun chatbotAIService(): ChatbotAIService = chatbotAIService
    private val chatbotAIService = mockk<ChatbotAIService>()

    @MockBean(PixKeyManagement::class)
    fun pixKeyManagement(): PixKeyManagement = pixKeyManagement
    private val pixKeyManagement = mockk<PixKeyManagement>()

    @MockBean(SubscriptionService::class)
    fun subscriptionService(): SubscriptionService = subscriptionService
    private val subscriptionService = mockk<SubscriptionService>()

    @MockBean(FindBillService::class)
    fun findBillService(): FindBillService = findBillService
    private val findBillService = mockk<FindBillService>()

    @MockBean(InAppSubscriptionService::class)
    fun inAppSubscriptionService(): InAppSubscriptionService = inAppSubscriptionService
    private val inAppSubscriptionService = mockk<InAppSubscriptionService>()

    @MockBean(MessageProcessorService::class)
    fun messageProcessorService(): MessageProcessorService = messageProcessorService
    private val messageProcessorService = mockk<MessageProcessorService>()

    @MockBean(ITPService::class)
    fun itpService(): ITPService = itpService
    private val itpService = mockk<ITPService>()

    @MockBean(OpenFinanceConsentService::class)
    fun openFinanceConsentService(): OpenFinanceConsentService = openFinanceConsentService
    private val openFinanceConsentService = mockk<OpenFinanceConsentService> {
        every { getAuthorizedSweepingConsents(any()) } returns activeConsents
    }

    @MockBean(WalletLimitsService::class)
    fun walletLimitsService(): WalletLimitsService = walletLimitsService
    private val walletLimitsService = mockk<WalletLimitsService> {
        every { getWhatsAppPaymentLimit(any()) } returns DailyPaymentLimit(
            updatedAt = getZonedDateTime(),
            amount = 100L,
            lastAmount = null,
            type = DailyPaymentLimitType.WHATSAPP_PAYMENT,
        )

        every {
            getAvailableLimit(any())
        } returns 100_00
    }

    @MockBean(SystemActivityRepository::class)
    fun systemActivityRepository(): SystemActivityRepository = systemActivityRepository
    private val systemActivityRepository = mockk<SystemActivityRepository>(relaxed = true)

    @MockBean(WalletService::class)
    fun walletService(): WalletService = walletService
    private val walletService = mockk<WalletService>(relaxUnitFun = true) {
        every { findWallets(any<AccountId>()) } returns listOf(wallet)
    }

    @MockBean(BillComingDueService::class)
    fun billsComingDueService(): BillComingDueService = billsComingDueService
    private val billsComingDueService = mockk<BillComingDueService>()

    @MockBean(ContactService::class)
    fun contactService(): ContactService = contactService
    private val contactService = mockk<ContactService>()

    @MockBean(AccountService::class)
    fun accountService(): AccountService = accountService
    private val accountService = mockk<AccountService>(relaxed = true) {
        every {
            findAccountById(any())
        } returns ACCOUNT.copy(configuration = ACCOUNT.configuration.copy(groups = listOf(AccountGroup.ALPHA)))

        every {
            getChatbotType(any<AccountId>())
        } returns ChatbotType.CHABOT_AI
    }

    @MockBean(AccountRegisterRepository::class)
    fun accountRegisterRepository(): AccountRegisterRepository = accountRegisterRepository
    private val accountRegisterRepository = mockk<AccountRegisterRepository>(relaxed = true) {
        every {
            findByAccountId(any())
        } returns mockk(relaxed = true)
    }

    @MockBean(AccountRepository::class)
    fun accountRepository(): AccountRepository = accountRepository
    private val accountRepository = mockk<AccountRepository>(relaxed = true)

    @MockBean(LoginService::class)
    fun loginService(): LoginService = loginService
    private val loginService = mockk<LoginService>() {
        every {
            findProviderUser(
                emailAddress = EmailAddress(email = "$<EMAIL>"),
                providerName = ProviderName.WHATSAPP,
            )
        } returns providerUser

        every {
            resolveLogin(any())
        } returns LoginResult(
            login = Login(
                accountId = AccountId(ACCOUNT_ID),
                emailAddress = EmailAddress(
                    EMAIL,
                ),
                role = Role.OWNER,
            ),
            created = false,
        )
    }

    @MockBean(BillRepository::class)
    fun billRepository() = billRepository
    private val billRepository = mockk<BillRepository> {
        every { findByWalletAndEffectiveDueDate(any(), any()) } returns listOf()
    }

    @DisplayName("ao validar a permissão de acesso")
    @Nested
    inner class SecurityTest {
        @Test
        fun `deve retornar UNAUTHORIZED quando não estiver autenticado`() {
            val request = HttpRequest.GET<Unit>("/chatbotAI/payment-organizations")

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(request, Argument.of(String::class.java), Argument.of(String::class.java))
            }

            thrown.status shouldBe HttpStatus.UNAUTHORIZED
        }

        @Test
        fun `deve retornar UNAUTHORIZED se a senha tiver errada`() {
            val request =
                HttpRequest.GET<Unit>("/chatbotAI/payment-organizations").basicAuth(validPhone, "INVALID_SECRET")

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(request, Argument.of(String::class.java), Argument.of(String::class.java))
            }

            thrown.status shouldBe HttpStatus.UNAUTHORIZED
        }

        @Test
        fun `deve retornar UNAUTHORIZED se não achar o login do usuário`() {
            every {
                loginService.findProviderUser(
                    emailAddress = EmailAddress(email = "$<EMAIL>"),
                    providerName = ProviderName.WHATSAPP,
                )
            } returns null

            every {
                loginService.findProviderUser(
                    emailAddress = EmailAddress(email = "<EMAIL>"),
                    providerName = ProviderName.WHATSAPP,
                )
            } returns null

            val request =
                HttpRequest.GET<Unit>("/chatbotAI/payment-organizations").basicAuth("*************", "CHATBOT_AI_SECRET")

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(request, Argument.of(String::class.java), Argument.of(String::class.java))
            }

            thrown.status shouldBe HttpStatus.UNAUTHORIZED
        }

        @Test
        fun `deve retornar FORBIDDEN se a senha tiver correta e o usuário não for um OWNER`() {
            every {
                loginService.resolveLogin(
                    providerUser = providerUser,
                )
            } returns LoginResult(
                login = Login(
                    accountId = AccountId(ACCOUNT_ID),
                    emailAddress = EmailAddress(
                        EMAIL,
                    ),
                    role = Role.GUEST,
                ),
                created = false,
            )
            val request =
                HttpRequest.GET<Unit>("/chatbotAI/payment-organizations").basicAuth(validPhone, "CHATBOT_AI_SECRET")

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(request, Argument.of(String::class.java), Argument.of(String::class.java))
            }

            thrown.status shouldBe HttpStatus.FORBIDDEN
        }

        @Test
        fun `deve retornar o resultado quando login estiver salvo sem o 9`() {
            every {
                loginService.findProviderUser(
                    emailAddress = EmailAddress(email = "$<EMAIL>"),
                    providerName = ProviderName.WHATSAPP,
                )
            } returns null

            val newProviderUser = ProviderUser(
                id = "123",
                emailAddress = EmailAddress(email = "<EMAIL>"),
                providerName = ProviderName.WHATSAPP,
                username = "meu nome",
            )
            every {
                loginService.findProviderUser(
                    emailAddress = EmailAddress(email = "<EMAIL>"),
                    providerName = ProviderName.WHATSAPP,
                )
            } returns newProviderUser

            every {
                itpService.listPaymentOrganizations(any())
            } returns GetAccountOrganizations(
                size = 0,
                document = Document(value = "***********"),
                otherFinancialInstitutions = listOf(),
                result = mapOf(),
            ).right()

            val request =
                HttpRequest.GET<Unit>("/chatbotAI/payment-organizations").basicAuth("*************", "CHATBOT_AI_SECRET")

            val response = client.toBlocking()
                .exchange(
                    request,
                    Argument.of(ListPaymentOrganizationsResponseTO::class.java),
                    Argument.of(String::class.java),
                )

            response.status shouldBe HttpStatus.OK
        }

        @Test
        fun `deve retornar o resultado quando estiver autenticado`() {
            val request =
                HttpRequest.GET<Unit>("/chatbotAI/payment-organizations").basicAuth(validPhone, "CHATBOT_AI_SECRET")
            every {
                itpService.listPaymentOrganizations(any())
            } returns GetAccountOrganizations(
                size = 0,
                document = Document(value = "***********"),
                otherFinancialInstitutions = listOf(),
                result = mapOf(),
            ).right()

            val response = client.toBlocking()
                .exchange(
                    request,
                    Argument.of(ListPaymentOrganizationsResponseTO::class.java),
                    Argument.of(String::class.java),
                )

            response.status shouldBe HttpStatus.OK
        }
    }

    @DisplayName("ao validar a chave Pix")
    @Nested
    inner class ValidatePixKeyTest {
        @Test
        fun `deve retornar sucesso se a chave do pix for válida e estiver abaixo do limite do assistente e do pix`() {
            val validPixKey = PixKey("***********", PixKeyType.CPF)
            every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns 100L
            every { walletLimitsService.getAvailableLimit(any()) } returns 100L
            every {
                pixKeyManagement.findKeyDetails(
                    any(),
                    any(),
                )
            } returns Either.Right(
                PixKeyDetailsResult(
                    PixKeyDetails(
                        key = validPixKey,
                        holder = PixKeyHolder(
                            accountNo = BigInteger("4321"),
                            accountDv = "7",
                            ispb = "********",
                            institutionName = "Test",
                            accountType = AccountType.CHECKING,
                            routingNo = 1234,
                        ),
                        owner = PixKeyOwner(
                            name = "Teste",
                            document = "***********",
                        ),
                    ),
                    "e2e",
                ),
            )

            val request = HttpRequest.POST(
                "/chatbotAI/pix/validate",
                ValidatePixTO(
                    keyType = validPixKey.type,
                    keyValue = validPixKey.value,
                    amount = 100L,
                ),
            ).basicAuth(validPhone, "CHATBOT_AI_SECRET")

            val response = client.toBlocking().exchange(request, Argument.of(ValidatePixResponseTO::class.java), Argument.of(ResponseTO::class.java))

            response.status shouldBe HttpStatus.OK
            response.body().code shouldBe "0"
            response.body().pixDetails.keyValue shouldBe validPixKey.value
            response.body().pixDetails.recipientInstitution shouldBe "Test"
            response.body().pixDetails.recipientName shouldBe "Teste"
            response.body().pixDetails.recipientDocument shouldBe "***********"
            response.body().pixDetails.keyType shouldBe validPixKey.type
        }

        @Test
        fun `deve retornar sucesso se a chave do pix for válida e estiver abaixo do limite do pix`() {
            val validPixKey = PixKey("***********", PixKeyType.CPF)
            every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns 100L
            every { walletLimitsService.getAvailableLimit(any()) } returns 10L
            every {
                pixKeyManagement.findKeyDetails(
                    any(),
                    any(),
                )
            } returns Either.Right(
                PixKeyDetailsResult(
                    PixKeyDetails(
                        key = validPixKey,
                        holder = PixKeyHolder(
                            accountNo = BigInteger("4321"),
                            accountDv = "7",
                            ispb = "********",
                            institutionName = "Test",
                            accountType = AccountType.CHECKING,
                            routingNo = 1234,
                        ),
                        owner = PixKeyOwner(
                            name = "Teste",
                            document = "***********",
                        ),
                    ),
                    "e2e",
                ),
            )

            val request = HttpRequest.POST(
                "/chatbotAI/pix/validate",
                ValidatePixTO(
                    keyType = validPixKey.type,
                    keyValue = validPixKey.value,
                    amount = 100L,
                ),
            ).basicAuth(validPhone, "CHATBOT_AI_SECRET")

            val response = client.toBlocking().exchange(request, Argument.of(ValidatePixResponseTO::class.java), Argument.of(ResponseTO::class.java))

            response.status shouldBe HttpStatus.OK
            response.body().code shouldBe "4006"
            response.body().pixDetails.keyValue shouldBe validPixKey.value
            response.body().pixDetails.recipientInstitution shouldBe "Test"
            response.body().pixDetails.recipientName shouldBe "Teste"
            response.body().pixDetails.recipientDocument shouldBe "***********"
            response.body().pixDetails.keyType shouldBe validPixKey.type
        }

        @Test
        fun `deve retornar sucesso se a chave do pix for válida e estiver abaixo do limite do assistente`() {
            val validPixKey = PixKey("***********", PixKeyType.CPF)
            every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns 10L
            every { walletLimitsService.getAvailableLimit(any()) } returns 100L
            every {
                pixKeyManagement.findKeyDetails(
                    any(),
                    any(),
                )
            } returns Either.Right(
                PixKeyDetailsResult(
                    PixKeyDetails(
                        key = validPixKey,
                        holder = PixKeyHolder(
                            accountNo = BigInteger("4321"),
                            accountDv = "7",
                            ispb = "********",
                            institutionName = "Test",
                            accountType = AccountType.CHECKING,
                            routingNo = 1234,
                        ),
                        owner = PixKeyOwner(
                            name = "Teste",
                            document = "***********",
                        ),
                    ),
                    "e2e",
                ),
            )

            val request = HttpRequest.POST(
                "/chatbotAI/pix/validate",
                ValidatePixTO(
                    keyType = validPixKey.type,
                    keyValue = validPixKey.value,
                    amount = 100L,
                ),
            ).basicAuth(validPhone, "CHATBOT_AI_SECRET")

            val response = client.toBlocking().exchange(request, Argument.of(ValidatePixResponseTO::class.java), Argument.of(ResponseTO::class.java))

            response.status shouldBe HttpStatus.OK
            response.body().code shouldBe "4004"
            response.body().pixDetails.keyValue shouldBe validPixKey.value
            response.body().pixDetails.recipientInstitution shouldBe "Test"
            response.body().pixDetails.recipientName shouldBe "Teste"
            response.body().pixDetails.recipientDocument shouldBe "***********"
            response.body().pixDetails.keyType shouldBe validPixKey.type
        }

        @Test
        fun `deve retornar BAD_REQUEST se a chave do pix não for válida`() {
            val invalidPixKey = PixKey("invalid-pix-key", PixKeyType.CPF)

            val request = HttpRequest.POST(
                "/chatbotAI/pix/validate",
                ValidatePixTO(
                    keyType = invalidPixKey.type,
                    keyValue = invalidPixKey.value,
                    amount = 100L,
                ),
            ).basicAuth(validPhone, "CHATBOT_AI_SECRET")

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(request, Argument.of(ValidatePixResponseTO::class.java))
            }

            thrown.status shouldBe HttpStatus.BAD_REQUEST
        }

        @Test
        fun `deve retornar BAD_REQUEST se o validatePix retornar que chave do pix não é válida`() {
            val validPixKey = PixKey("***********", PixKeyType.CPF)

            every {
                pixKeyManagement.findKeyDetails(
                    any(),
                    any(),
                )
            } returns PixKeyError.MalformedKey.left()

            val request = HttpRequest.POST(
                "/chatbotAI/pix/validate",
                ValidatePixTO(
                    keyType = validPixKey.type,
                    keyValue = validPixKey.value,
                    amount = 100L,
                ),
            ).basicAuth(validPhone, "CHATBOT_AI_SECRET")

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(request, Argument.of(ValidatePixResponseTO::class.java))
            }

            thrown.status shouldBe HttpStatus.BAD_REQUEST
        }

        @Test
        fun `deve retornar NOT_FOUND se a chave do pix não for encontrada`() {
            val validPixKey = PixKey("***********", PixKeyType.CPF)

            every {
                pixKeyManagement.findKeyDetails(
                    any(),
                    any(),
                )
            } returns PixKeyError.KeyNotFound.left()

            val request = HttpRequest.POST(
                "/chatbotAI/pix/validate",
                ValidatePixTO(
                    keyType = validPixKey.type,
                    keyValue = validPixKey.value,
                    amount = 100L,
                ),
            ).basicAuth(validPhone, "CHATBOT_AI_SECRET")

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(request, Argument.of(ValidatePixResponseTO::class.java))
            }

            thrown.status shouldBe HttpStatus.NOT_FOUND
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.chatbotai.adapters.api.ChatbotAIControllerIntegrationTest#serverError")
        fun `deve retornar serverError se a chave do pix não conseguir validar`(error: PixKeyError) {
            val validPixKey = PixKey("***********", PixKeyType.CPF)

            every {
                pixKeyManagement.findKeyDetails(
                    any(),
                    any(),
                )
            } returns error.left()

            val request = HttpRequest.POST(
                "/chatbotAI/pix/validate",
                ValidatePixTO(
                    keyType = validPixKey.type,
                    keyValue = validPixKey.value,
                    amount = 100L,
                ),
            ).basicAuth(validPhone, "CHATBOT_AI_SECRET")

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(request, Argument.of(ValidatePixResponseTO::class.java))
            }

            thrown.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        }
    }

    @DisplayName("ao solicitar um Pix")
    @Nested
    inner class RequestPixTest {
        @Test
        fun `deve retornar bad request quando tentar agendar um pix com valor maior que o limite diário`() {
            every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns 100L
            every { walletLimitsService.getAvailableLimit(any()) } returns 0L
            val request = HttpRequest.POST(
                "/chatbotAI/pix",
                CreateChatbotPixTO(
                    recipient = RequestPixRecipientTO(
                        name = "John Doe",
                        pixKey = PixKeyRequestTO("***********", PixKeyType.CPF),
                        document = "***********",
                        documentType = "CPF",
                        bankDetails = null,
                    ),
                    amount = 1000,
                    description = "Teste",
                    dueDate = "2024-02-15",
                    transactionId = "CHATBOT-TRANSACTION-ID",
                    sweepingRequest = null,
                    retryTransaction = false,
                ),
            ).basicAuth(validPhone, "CHATBOT_AI_SECRET")

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(request, Argument.of(CreateChatbotPixTO::class.java), Argument.of(ResponseTO::class.java))
            }

            thrown.status shouldBe HttpStatus.BAD_REQUEST
            thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4006"
            thrown.response.getBody(ResponseTO::class.java).get().message shouldBe "Daily limit exceeded"
        }

        @Test
        fun `deve retornar bad request quando tentar agendar um pix com valor maior que o limite do assistente`() {
            every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns 100L
            every { walletLimitsService.getAvailableLimit(any()) } returns 1000L
            val request = HttpRequest.POST(
                "/chatbotAI/pix",
                CreateChatbotPixTO(
                    recipient = RequestPixRecipientTO(
                        name = "John Doe",
                        pixKey = PixKeyRequestTO("***********", PixKeyType.CPF),
                        document = "***********",
                        documentType = "CPF",
                        bankDetails = null,
                    ),
                    amount = 1000,
                    description = "Teste",
                    dueDate = "2024-02-15",
                    transactionId = "TRANSACTION-ID",
                    sweepingRequest = null,
                    retryTransaction = false,
                ),
            ).basicAuth(validPhone, "CHATBOT_AI_SECRET")

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(request, Argument.of(CreateChatbotPixTO::class.java), Argument.of(ResponseTO::class.java))
            }

            thrown.status shouldBe HttpStatus.BAD_REQUEST
            thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4004"
            thrown.response.getBody(ResponseTO::class.java).get().message shouldBe "Assistant limit exceeded"
        }
    }

    @DisplayName("ao solicitar o pagamento de contas")
    @Nested
    inner class ScheduleBillsTest {
        @Test
        fun `deve retornar bad request quando tentar agendar uma conta que não está ativa`() {
            every { walletService.findWallet(any()) } returns wallet
            every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns 100L
            every { findBillService.find(any(), any()) } returns getPaidBill()
            val request = HttpRequest.POST(
                "/chatbotAI/schedule",
                ChatbotScheduleBillsTO(
                    bills = listOf(getPaidBill().billId.value),
                ),
            ).basicAuth(validPhone, "CHATBOT_AI_SECRET")

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(request, Argument.of(ChatbotScheduleBillsTO::class.java), Argument.of(String::class.java))
            }

            thrown.status shouldBe HttpStatus.BAD_REQUEST
            thrown.message shouldBe """{"code":"4001","message":"Bill not active"}"""
        }

        @Test
        fun `deve retornar bad request quando tentar fazer uma transferência open finance maior do que o limite de whatsapp`() {
            every { walletService.findWallet(any()) } returns wallet
            every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns 100L
            every { findBillService.find(any(), any()) } returns getPaidBill().copy(status = BillStatus.ACTIVE)
            every { walletLimitsService.getAvailableLimit(any()) } returns 0L
            val request = HttpRequest.POST(
                "/chatbotAI/schedule",
                ChatbotScheduleBillsTO(
                    bills = listOf(getPaidBill().billId.value),
                    sweepingRequest = SweepingRequestTO(
                        amount = 1000L,
                        transactionId = ExternalTransactionId().toString(),
                    ),
                ),
            ).basicAuth(validPhone, "CHATBOT_AI_SECRET")

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(request, Argument.of(ChatbotScheduleBillsTO::class.java), Argument.of(String::class.java))
            }

            thrown.status shouldBe HttpStatus.BAD_REQUEST
            thrown.message shouldBe """{"code":"4004","message":"Assistant limit exceeded"}"""
        }
    }

    @DisplayName("ao validar os limites de pagamento")
    @Nested
    inner class PaymentLimitTest {
        @Test
        fun `deve retornar o status do pagamento via whatsApp e o limite`() {
            every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns 100L
            every { walletLimitsService.getAvailableLimit(any()) } returns 10L

            val request = HttpRequest.GET<AssistantPaymentLimit>("/chatbotAI/whatsAppPayment/wallet-id/limit").basicAuth(validPhone, "CHATBOT_AI_SECRET")
            val response = client.toBlocking()
                .exchange(
                    request,
                    Argument.of(AssistantPaymentLimit::class.java),
                    Argument.of(String::class.java),
                )

            response.status shouldBe HttpStatus.OK
            val body = response.body().shouldBeTypeOf<AssistantPaymentLimit>()
            body.amount shouldBe 10
            body.enabled shouldBe true
        }

        @Test
        fun `deve validar o valor de pagamento por pix se estiver abaixo do limite de pix e do whatsapp`() {
            every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns 100L
            every { walletLimitsService.getAvailableLimit(any()) } returns 100L

            val request = HttpRequest.POST(
                "/chatbotAI/limit/validate",
                AvailableLimitTO(
                    amount = 50,
                    type = AvailableLimitType.PIX,
                ),
            ).basicAuth(validPhone, "CHATBOT_AI_SECRET")
            val response = client.toBlocking()
                .exchange(
                    request,
                    Argument.of(ResponseTO::class.java),
                    Argument.of(String::class.java),
                )

            response.status shouldBe HttpStatus.OK
            val body = response.body().shouldBeTypeOf<ResponseTO>()
            body.code shouldBe "0"
            body.message shouldBe "Success"
        }

        @Test
        fun `deve validar o valor de pagamento de bills se estiver abaixo do limite do whatsapp`() {
            every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns 100L

            val request = HttpRequest.POST(
                "/chatbotAI/limit/validate",
                AvailableLimitTO(
                    amount = 50,
                    type = AvailableLimitType.SCHEDULE_BILLS,
                ),
            ).basicAuth(validPhone, "CHATBOT_AI_SECRET")
            val response = client.toBlocking()
                .exchange(
                    request,
                    Argument.of(ResponseTO::class.java),
                    Argument.of(String::class.java),
                )

            response.status shouldBe HttpStatus.OK
            val body = response.body().shouldBeTypeOf<ResponseTO>()
            body.code shouldBe "0"
            body.message shouldBe "Success"
        }

        @Test
        fun `não deve validar o valor de pagamento de bills se estiver acima do limite do whatsapp`() {
            every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns 100L

            val request = HttpRequest.POST(
                "/chatbotAI/limit/validate",
                AvailableLimitTO(
                    amount = 150,
                    type = AvailableLimitType.SCHEDULE_BILLS,
                ),
            ).basicAuth(validPhone, "CHATBOT_AI_SECRET")
            val response = client.toBlocking()
                .exchange(
                    request,
                    Argument.of(ResponseTO::class.java),
                    Argument.of(String::class.java),
                )

            response.status shouldBe HttpStatus.OK
            val body = response.body().shouldBeTypeOf<ResponseTO>()
            body.code shouldBe "4004"
            body.message shouldBe "Assistant limit exceeded"
        }

        @Test
        fun `não deve validar o valor de pagamento por pix se estiver acima do limite de pix`() {
            every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns 100L
            every { walletLimitsService.getAvailableLimit(any()) } returns 10L

            val request = HttpRequest.POST(
                "/chatbotAI/limit/validate",
                AvailableLimitTO(
                    amount = 50,
                    type = AvailableLimitType.PIX,
                ),
            ).basicAuth(validPhone, "CHATBOT_AI_SECRET")
            val response = client.toBlocking()
                .exchange(
                    request,
                    Argument.of(ResponseTO::class.java),
                    Argument.of(String::class.java),
                )

            response.status shouldBe HttpStatus.OK
            val body = response.body().shouldBeTypeOf<ResponseTO>()
            body.code shouldBe "4006"
            body.message shouldBe "Daily limit exceeded"
        }

        @Test
        fun `não deve validar o valor de pagamento por pix se estiver acima do limite de pix do whatsapp`() {
            every { walletLimitsService.getAvailableAssistantLimit(any(), any()) } returns 10L
            every { walletLimitsService.getAvailableLimit(any()) } returns 10L

            val request = HttpRequest.POST(
                "/chatbotAI/limit/validate",
                AvailableLimitTO(
                    amount = 50,
                    type = AvailableLimitType.PIX,
                ),
            ).basicAuth(validPhone, "CHATBOT_AI_SECRET")
            val response = client.toBlocking()
                .exchange(
                    request,
                    Argument.of(ResponseTO::class.java),
                    Argument.of(String::class.java),
                )

            response.status shouldBe HttpStatus.OK
            val body = response.body().shouldBeTypeOf<ResponseTO>()
            body.code shouldBe "4006"
            body.message shouldBe "Daily limit exceeded"
        }
    }

    @DisplayName("ao consultar a assinatura")
    @Nested
    inner class SubscriptionTest {
        @Test
        fun `deve retornar o status da assinatura quando o usuário tiver uma assinatura PIX`() {
            every { accountService.findAccountById(any()) } returns ACCOUNT.copy(subscriptionType = SubscriptionType.PIX)
            every { subscriptionService.findOrNull(any()) } returns Subscription(accountId = AccountId(value = "ipsum"), document = Document(value = DOCUMENT), status = SubscriptionStatus.ACTIVE, amount = 990, dayOfMonth = 10, recurrenceId = RecurrenceId(value = "quis"), paymentStatus = SubscriptionPaymentStatus.PAID, walletId = WalletId(value = "wallet"), nextEffectiveDueDate = getLocalDate())

            val request = HttpRequest.GET<SubscriptionResponseTO>("/chatbotAI/subscription").basicAuth(validPhone, "CHATBOT_AI_SECRET")
            val response = client.toBlocking()
                .exchange(
                    request,
                    Argument.of(SubscriptionResponseTO::class.java),
                    Argument.of(String::class.java),
                )

            response.status shouldBe HttpStatus.OK
            val body = response.body().shouldBeTypeOf<SubscriptionResponseTO>()
            body.subscription.shouldNotBeNull()
            body.subscription!!.fee shouldBe 990
            body.subscription!!.paymentStatus shouldBe "PAID"
            body.subscription!!.type shouldBe SubscriptionType.PIX
        }

        @Test
        fun `deve retornar null quando usuário não tiver assinatura PIX`() {
            every { subscriptionService.findOrNull(any()) } returns null
            every { accountService.findAccountById(any()) } returns ACCOUNT.copy(subscriptionType = SubscriptionType.PIX)

            val request = HttpRequest.GET<SubscriptionResponseTO>("/chatbotAI/subscription").basicAuth(validPhone, "CHATBOT_AI_SECRET")
            val response = client.toBlocking()
                .exchange(
                    request,
                    Argument.of(SubscriptionResponseTO::class.java),
                    Argument.of(String::class.java),
                )

            response.status shouldBe HttpStatus.OK
            val body = response.body().shouldBeTypeOf<SubscriptionResponseTO>()
            body.subscription.shouldBeNull()
        }

        @Test
        fun `deve retornar a assinatura in_app quando o usuário tiver uma`() {
            every { inAppSubscriptionService.getSubscription(any()) } returns InAppSubscription(
                accountId = AccountId("teste"),
                endsAt = getZonedDateTime(),
                autoRenew = true,
                inAppSubscriptionAccessConcessionId = InAppSubscriptionAccessConcessionId("teste"),
                reason = InAppSubscriptionReason.SUBSCRIPTION,
                status = InAppSubscriptionStatus.ACTIVE,
                store = InAppSubscriptionStore.PLAY_STORE,
                price = 999,
                productId = InAppSubscriptionProductId("teste"),
                offStoreProductId = null,
            )
            every { accountService.findAccountById(any()) } returns ACCOUNT.copy(subscriptionType = SubscriptionType.IN_APP)

            val request = HttpRequest.GET<SubscriptionResponseTO>("/chatbotAI/subscription").basicAuth(validPhone, "CHATBOT_AI_SECRET")
            val response = client.toBlocking()
                .exchange(
                    request,
                    Argument.of(SubscriptionResponseTO::class.java),
                    Argument.of(String::class.java),
                )

            response.status shouldBe HttpStatus.OK
            val body = response.body().shouldBeTypeOf<SubscriptionResponseTO>()
            body.subscription.shouldNotBeNull()
            body.subscription!!.fee shouldBe null
            body.subscription!!.paymentStatus shouldBe "PAID"
            body.subscription!!.type shouldBe SubscriptionType.IN_APP
        }

        @Test
        fun `deve retornar null quando usuário não tiver assinatura IN_APP`() {
            every { inAppSubscriptionService.getSubscription(any()) } returns null
            every { accountService.findAccountById(any()) } returns ACCOUNT.copy(subscriptionType = SubscriptionType.IN_APP)

            val request = HttpRequest.GET<SubscriptionResponseTO>("/chatbotAI/subscription").basicAuth(validPhone, "CHATBOT_AI_SECRET")
            val response = client.toBlocking()
                .exchange(
                    request,
                    Argument.of(SubscriptionResponseTO::class.java),
                    Argument.of(String::class.java),
                )

            response.status shouldBe HttpStatus.OK
            val body = response.body().shouldBeTypeOf<SubscriptionResponseTO>()
            body.subscription.shouldBeNull()
        }
    }

    @Nested
    @DisplayName("chooseChatbot")
    inner class ChooseChatbotTest {

        @ParameterizedTest
        @EnumSource(value = AccountStatus::class, mode = EnumSource.Mode.INCLUDE, names = ["UNDER_REVIEW", "UNDER_EXTERNAL_REVIEW", "APPROVED"])
        fun `deve retornar que o tipo é chatbot ai quando usuário for guest e estiver com o cadastro em revisão`(accountStatus: AccountStatus) {
            every {
                accountRepository.findById(any())
            } throws AccountNotFoundException("")

            every {
                accountRepository.findPartialAccountById(any())
            } returns PARTIAL_ACCOUNT.copy(status = accountStatus)

            val request = HttpRequest.GET<SubscriptionResponseTO>("/chatbot/choose").headers(mapOf("X-EXTERNAL-USER-ID" to "$<EMAIL>"))
            val response = client.toBlocking()
                .exchange(
                    request,
                    Argument.of(ChatbotChooseTO::class.java),
                    Argument.of(String::class.java),
                )

            response.status shouldBe HttpStatus.OK
            response.getBody(Argument.of(ChatbotChooseTO::class.java)).get().name shouldBe ChatbotType.CHABOT_AI.value
        }

        @ParameterizedTest
        @EnumSource(value = AccountStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["UNDER_REVIEW", "UNDER_EXTERNAL_REVIEW", "APPROVED"])
        fun `deve retornar que o tipo é chatbot legado quando usuário for guest mas não tiver com a conta em estado de revisão`(accountStatus: AccountStatus) {
            every {
                accountRepository.findById(any())
            } throws AccountNotFoundException("")

            every {
                accountRepository.findPartialAccountById(any())
            } returns PARTIAL_ACCOUNT.copy(status = accountStatus)

            val request = HttpRequest.GET<SubscriptionResponseTO>("/chatbot/choose").headers(mapOf("X-EXTERNAL-USER-ID" to "$<EMAIL>"))
            val response = client.toBlocking()
                .exchange(
                    request,
                    Argument.of(ChatbotChooseTO::class.java),
                    Argument.of(String::class.java),
                )

            response.status shouldBe HttpStatus.OK
            response.getBody(Argument.of(ChatbotChooseTO::class.java)).get().name shouldBe ChatbotType.CHATBOT_LEGACY.value
        }
    }

    @DisplayName("systemAcitivity")
    @Nested
    inner class SystemActivityTest {
        @Test
        fun `deve retornar a lista de system activities se existir`() {
            every { systemActivityRepository.find(any(), any()) } returns SystemActivity(
                key = SystemActivityKey(
                    value = ACCOUNT_ID,
                    type = SystemActivityKeyType.Account,
                ),
                lastUpdated = getZonedDateTime(),
                value = "1",
            )

            val requestBody = SystemActivityRequestTO(listOf(SystemActivityType.PromotedSweepingAccountOptOut.name, "NonExistentType"))

            val request = HttpRequest.POST("/chatbotAI/systemActivities", requestBody).basicAuth(validPhone, "CHATBOT_AI_SECRET")
            val response = client.toBlocking()
                .exchange(
                    request,
                    Argument.of(SystemActivityResponseTO::class.java),
                    Argument.of(String::class.java),
                )

            response.status shouldBe HttpStatus.OK
            val body = response.getBody(Argument.of(SystemActivityResponseTO::class.java)).get()
            body.systemActivities.size shouldBe 1
            body.systemActivities[0].systemActivityType shouldBe SystemActivityType.PromotedSweepingAccountOptOut.name
        }

        @Test
        fun `deve retornar uma lista vazia se não achar system activities`() {
            every { systemActivityRepository.find(any(), any()) } returns SystemActivity(
                key = SystemActivityKey(
                    value = ACCOUNT_ID,
                    type = SystemActivityKeyType.Account,
                ),
                lastUpdated = getZonedDateTime(),
                value = "1",
            )

            val requestBody = SystemActivityRequestTO(listOf("NonExistentType", "NonExistentType2"))

            val request = HttpRequest.POST("/chatbotAI/systemActivities", requestBody).basicAuth(validPhone, "CHATBOT_AI_SECRET")
            val response = client.toBlocking()
                .exchange(
                    request,
                    Argument.of(SystemActivityResponseTO::class.java),
                    Argument.of(String::class.java),
                )

            response.status shouldBe HttpStatus.OK
            val body = response.getBody(Argument.of(SystemActivityResponseTO::class.java)).get()
            body.systemActivities.size shouldBe 0
        }

        @Test
        fun `deve conseguir salvar o system activity se existir o tipo`() {
            val requestBody = SystemActivityTO(SystemActivityType.PromotedSweepingAccountOptOut.name, true)

            val request = HttpRequest.POST("/chatbotAI/systemActivity", requestBody).basicAuth(validPhone, "CHATBOT_AI_SECRET")
            val response = client.toBlocking()
                .exchange(
                    request,
                    Argument.of(String::class.java),
                    Argument.of(String::class.java),
                )

            response.status shouldBe HttpStatus.OK
        }

        @Test
        fun `deve retornar BadRequest quando não existir o tipo`() {
            val requestBody = SystemActivityTO("NonExistentType", true)

            val request = HttpRequest.POST("/chatbotAI/systemActivity", requestBody).basicAuth(validPhone, "CHATBOT_AI_SECRET")

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(
                        request,
                        Argument.of(String::class.java),
                        Argument.of(String::class.java),
                    )
            }
            thrown.status shouldBe HttpStatus.BAD_REQUEST
        }

        @Test
        fun `deve retornar internal server error quando der um erro não esperado`() {
            every { systemActivityRepository.save(any(), any(), any()) } throws RuntimeException("teste")
            val requestBody = SystemActivityTO(SystemActivityType.PromotedSweepingAccountOptOut.name, true)

            val request = HttpRequest.POST("/chatbotAI/systemActivity", requestBody).basicAuth(validPhone, "CHATBOT_AI_SECRET")

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(
                        request,
                        Argument.of(String::class.java),
                        Argument.of(String::class.java),
                    )
            }
            thrown.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        }
    }

    @Test
    fun `deve retornar a lista de contas vazia e o status do guest`() {
        every { openFinanceConsentService.getAuthorizedSweepingConsents(any()) } returns emptyList()
        val request = HttpRequest.GET<SubscriptionResponseTO>("/chatbotAI/pending-bills?startDate=2024-03-03&endDate=2024-03-03").basicAuth(validPhone, "CHATBOT_AI_SECRET")
        every {
            accountService.findAccountById(any())
        } throws AccountNotFoundException("")

        every {
            accountService.findPartialAccountById(any())
        } returns PARTIAL_ACCOUNT.copy(status = AccountStatus.UNDER_EXTERNAL_REVIEW)

        val response = client.toBlocking()
            .exchange(
                request,
                Argument.of(AccountAndWalletsTO::class.java),
                Argument.of(String::class.java),
            )

        val body = response.body.get()
        response.status shouldBe HttpStatus.OK
        body.wallets.shouldBeEmpty()
        body.account.status shouldBe AccountStatus.UNDER_EXTERNAL_REVIEW
        body.account.paymentStatus shouldBe AccountPaymentStatus.UpToDate
    }

    companion object {
        @JvmStatic
        fun serverError(): Stream<Arguments> = Stream.of(
            arguments(PixKeyError.UnknownError),
            arguments(PixKeyError.SystemUnavailable),
        )
    }
}