package ai.friday.billpayment.modules.eventapi.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.AbstractAsyncDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.AbstractDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.USER_EVENT_TABLE_NAME
import io.micronaut.context.annotation.Property
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient

private const val DYNAMODB_USER_EVENT_TABLE_NAME = "dynamodb.userEventsTableName"

abstract class AbstractUserEventDynamoDAO<T>(
    cli: DynamoDbEnhancedClient,
    type: Class<T>,
) : AbstractDynamoDAO<T>(
    cli = cli,
    type = type,
) {
    private val logger = LoggerFactory.getLogger(AbstractUserEventDynamoDAO::class.java)

    @field:Property(name = DYNAMODB_USER_EVENT_TABLE_NAME)
    private var tName: String? = null

    override val tableName by lazy {
        logger.info("AbstractUserEventDynamoDAO#tableName: $tName")
        tName ?: USER_EVENT_TABLE_NAME
    }
}

abstract class AbstractUserEventDynamoDAOAsync<T>(
    cli: DynamoDbEnhancedAsyncClient,
    type: Class<T>,
) : AbstractAsyncDynamoDAO<T>(
    cli = cli,
    type = type,
) {
    private val logger = LoggerFactory.getLogger(AbstractUserEventDynamoDAOAsync::class.java)

    @field:Property(name = DYNAMODB_USER_EVENT_TABLE_NAME)
    private var tName: String? = null

    override val tableName by lazy {
        logger.info("AbstractUserEventDynamoDAOAsync#tableName: $tName")
        tName ?: USER_EVENT_TABLE_NAME
    }
}