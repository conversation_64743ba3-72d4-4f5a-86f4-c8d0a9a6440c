package ai.friday.billpayment.app.vehicledebts

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.VEHICLE_ACTIVE
import ai.friday.billpayment.VEHICLE_INACTIVE
import ai.friday.billpayment.VEHICLE_PENDING
import ai.friday.billpayment.adapters.vehicledebts.VehicleDebtsAdapter
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.ConcessionariaService
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.ExternalBillId
import ai.friday.billpayment.app.bill.ExternalBillProvider
import ai.friday.billpayment.app.bill.FichaCompensacaoService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.manualentry.ManualEntry
import ai.friday.billpayment.app.manualentry.ManualEntryId
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.app.manualentry.ManualEntryStatus
import ai.friday.billpayment.app.manualentry.ManualEntryType
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAdded2
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billRegisterData
import ai.friday.billpayment.integration.CONCESSIONARIA_DIGITABLE_LINE
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import arrow.core.right
import io.kotest.matchers.result.shouldBeFailure
import io.kotest.matchers.result.shouldBeSuccess
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class VehicleDebtsServiceTest {
    private val vehicleDebtsAdapter: VehicleDebtsAdapter = mockk {
        every {
            enrollByUser(any(), any())
        } returns Result.success(Unit)
        every {
            enrollByVehicle(any(), any(), any(), any(), any(), any())
        } returns Result.success(Unit)
        every {
            getAllVehicleByUser(any(), any())
        } returns Result.success(listOf(VEHICLE_ACTIVE, VEHICLE_PENDING, VEHICLE_INACTIVE))
        every {
            withdrawByVehicle(any(), any(), any())
        } returns Result.success(Unit)
    }

    private val concessionariaService: ConcessionariaService = mockk(relaxed = true)

    private val fichaCompensacaoService: FichaCompensacaoService = mockk(relaxed = true)

    private val accountServiceMock: AccountService = mockk {
        every { findAccountById(any()) } returns ACCOUNT
    }

    private val manualEntryService = mockk<ManualEntryService> {
        every { findAllByWalletAndDueDate(any(), any()) } returns emptyList()
        every { ignore(any(), any()) } returns Unit.right()
    }

    private val messagePublisherMock = mockk<MessagePublisher>(relaxed = true)

    private val vehicleDebtsService = VehicleDebtsService(
        vehicleDebtsAdapter = vehicleDebtsAdapter,
        fichaCompensacaoService = fichaCompensacaoService,
        concessionariaService = concessionariaService,
        accountService = accountServiceMock,
        manualEntryService = manualEntryService,
        crmRepository = mockk(relaxed = true),
        vehicleDebtsQueueName = "vehicle_debts_enrichment",
        messagePublisher = messagePublisherMock,
    )

    @Test
    fun `deve retornar a lista de veículos de um usuário`() {
        val result = vehicleDebtsService.getAllVehicles(ACCOUNT.accountId)

        verify {
            accountServiceMock.findAccountById(ACCOUNT.accountId)
            vehicleDebtsAdapter.getAllVehicleByUser(ACCOUNT.accountId, Document(ACCOUNT.document))
        }

        result.shouldBeSuccess()
        result.onSuccess {
            it.size.shouldBe(2)
        }
    }

    @Test
    fun `deve alterar o status de um enroll para inativo`() {
        val result = vehicleDebtsService.withdrawByVehicle(ACCOUNT.accountId, LicensePlate("ABC1234"))

        verify {
            vehicleDebtsAdapter.withdrawByVehicle(ACCOUNT.accountId, Document(ACCOUNT.document), LicensePlate("ABC1234"))
        }

        result.shouldBeSuccess()
    }

    @Test
    fun `deve retornar Unit ao cadastrar um usuário com sucesso`() {
        val result = vehicleDebtsService.enrollByUser(ACCOUNT.accountId, Document(ACCOUNT.document))

        verify {
            vehicleDebtsAdapter.enrollByUser(ACCOUNT.accountId, Document(ACCOUNT.document))
        }

        result.shouldBeSuccess()
    }

    @Test
    fun `deve retornar Exception ao falhar em cadastrar um usuário`() {
        every {
            vehicleDebtsAdapter.enrollByUser(any(), any())
        } returns Result.failure(Exception("Erro ao cadastrar usuário"))

        val result = vehicleDebtsService.enrollByUser(ACCOUNT.accountId, Document(ACCOUNT.document))

        verify {
            vehicleDebtsAdapter.enrollByUser(ACCOUNT.accountId, Document(ACCOUNT.document))
        }

        result.shouldBeFailure()
    }

    @Test
    fun `deve retornar Unit ao cadastrar uma placa com sucesso`() {
        val result = vehicleDebtsService.enrollByVehicle(ACCOUNT.accountId, Document(ACCOUNT.document), LicensePlate("ABC1234"), false, description = null, source = EnrollVehicleSource.APP)

        verify {
            vehicleDebtsAdapter.enrollByVehicle(ACCOUNT.accountId, Document(ACCOUNT.document), LicensePlate("ABC1234"), false, description = null, source = EnrollVehicleSource.APP)
        }

        result.shouldBeSuccess()
    }

    @Test
    fun `se o account estiver fechado, não deve cadastrar`() {
        every { accountServiceMock.findAccountById(any()) } throws IllegalStateException("")

        vehicleDebtsService.enrollByVehicle(ACCOUNT.accountId, LicensePlate("ABC1234"), false, null, EnrollVehicleSource.APP)
            .shouldBeFailure()
    }

    @Test
    fun `deve retornar Exception ao falhar em cadastrar uma placa`() {
        every {
            vehicleDebtsAdapter.enrollByVehicle(any(), any(), any(), any(), any(), any())
        } returns Result.failure(Exception("Erro ao cadastrar placa"))

        val result = vehicleDebtsService.enrollByVehicle(ACCOUNT.accountId, Document(ACCOUNT.document), LicensePlate("ABC1234"), false, "teste", source = EnrollVehicleSource.APP)

        verify {
            vehicleDebtsAdapter.enrollByVehicle(ACCOUNT.accountId, Document(ACCOUNT.document), LicensePlate("ABC1234"), false, "teste", source = EnrollVehicleSource.APP)
        }

        result.shouldBeFailure()
    }

    @Test
    fun `ao conseguir validar uma ficha deve criar uma bill do tipo ficha`() {
        every {
            fichaCompensacaoService.createFichaDeCompensacao(any(), any(), any())
        } returns CreateBillResult.SUCCESS(Bill.build(billAddedFicha), null, listOf())

        val request = buildRequest(
            barcode = billAddedFicha.barcode,
            paymentStatus = VehicleDebtsBillRequest.Status.ACTIVE,
            dueDate = LocalDate.now(),
        )

        vehicleDebtsService.handle(request)
            .shouldBeSuccess()

        verify {
            fichaCompensacaoService.createFichaDeCompensacao(any(), any(), any())
        }

        verify(exactly = 0) {
            concessionariaService.createConcessionaria(any(), any(), any())
        }
    }

    @Test
    fun `quando o status for ACTIVE deve enviar mensagem para a fila de enriquecimento`() {
        every {
            fichaCompensacaoService.createFichaDeCompensacao(any(), any(), any())
        } returns CreateBillResult.SUCCESS(Bill.build(billAddedFicha), null, listOf())

        val request = buildRequest(
            barcode = billAddedFicha.barcode,
            paymentStatus = VehicleDebtsBillRequest.Status.ACTIVE,
            dueDate = LocalDate.now(),
        )

        vehicleDebtsService.handle(request)
            .shouldBeSuccess()

        verify {
            messagePublisherMock.sendMessage(
                queueName = "vehicle_debts_enrichment",
                body = match<VehicleDebtEnrichmentMessage> {
                    it.accountId == request.accountId.value &&
                        it.licensePlate == request.licensePlate.value &&
                        it.barcode == request.barcode?.number &&
                        it.externalId == request.externalId
                },
                delaySeconds = null,
            )
        }
    }

    @Test
    fun `quando o status não for ACTIVE não deve enviar mensagem para a fila de enriquecimento`() {
        every { manualEntryService.create(any(), any(), any(), any(), any(), any(), any(), any(), any(), any()) } returns mockk<ManualEntry>(relaxed = true).right()

        val request = buildRequest(
            barcode = billAddedFicha.barcode,
            paymentStatus = VehicleDebtsBillRequest.Status.PAID,
            dueDate = LocalDate.now(),
        )

        vehicleDebtsService.handle(request)
            .shouldBeSuccess()

        verify(exactly = 0) {
            messagePublisherMock.sendMessage(
                queueName = "vehicle_debts_enrichment",
                body = any(),
                delaySeconds = any(),
            )
        }
    }

    @Test
    fun `quando não tiver barcode não deve enviar mensagem para a fila de enriquecimento`() {
        every { manualEntryService.create(any(), any(), any(), any(), any(), any(), any(), any(), any(), any()) } returns mockk<ManualEntry>(relaxed = true).right()

        val request = buildRequest(
            barcode = null,
            paymentStatus = VehicleDebtsBillRequest.Status.ACTIVE,
            dueDate = LocalDate.now(),
        )

        vehicleDebtsService.handle(request)
            .shouldBeSuccess()

        verify(exactly = 0) {
            messagePublisherMock.sendMessage(
                queueName = "vehicle_debts_enrichment",
                body = any(),
                delaySeconds = any(),
            )
        }
    }

    @Test
    fun `ao conseguir validar um boleto de concessionária deve criar uma bill do tipo concessionária`() {
        every {
            concessionariaService.createConcessionaria(any(), any(), any())
        } returns CreateBillResult.SUCCESS(Bill.build(billAdded2), null, listOf())

        val request = buildRequest(
            barcode = billAdded2.barcode!!,
            paymentStatus = VehicleDebtsBillRequest.Status.ACTIVE,
            dueDate = LocalDate.now(),
        )

        val result = vehicleDebtsService.handle(request)

        verify {
            concessionariaService.createConcessionaria(any(), any(), any())
        }

        verify(exactly = 0) {
            fichaCompensacaoService.createFichaDeCompensacao(any(), any(), any())
        }

        result.shouldBeSuccess()
    }

    @Test
    fun `quando vier uma conta que já foi um notice deve apagar o notice`() {
        every {
            concessionariaService.createConcessionaria(any(), any(), any())
        } returns CreateBillResult.SUCCESS(Bill.build(billAdded2), null, listOf())

        val request = buildRequest(
            barcode = billAdded2.barcode!!,
            paymentStatus = VehicleDebtsBillRequest.Status.ACTIVE,
            dueDate = LocalDate.now(),
            externalBillId = ExternalBillId("same-external", ExternalBillProvider.VEHICLE_DEBTS),
        )

        every { manualEntryService.findAllByWalletAndDueDate(any(), any()) } returns listOf(
            mockk<ManualEntry>(relaxed = true) {
                every { id } returns ManualEntryId("id")
                every { externalId } returns ExternalBillId("same-external", ExternalBillProvider.VEHICLE_DEBTS)
            },
        )

        vehicleDebtsService.handle(request)
            .shouldBeSuccess()

        verify {
            manualEntryService.ignore(ManualEntryId("id"), any())
        }
    }

    @ParameterizedTest
    @MethodSource("nonRetryableBillResultFailures")
    fun `quando a validação de concessionaria falhar e não for retentável, não deve criar um pagamento`(failures: CreateBillResult.FAILURE) {
        every {
            concessionariaService.createConcessionaria(any(), any(), any())
        } returns failures

        val request = buildRequest(
            barcode = billAdded2.barcode!!,
            paymentStatus = VehicleDebtsBillRequest.Status.ACTIVE,
            dueDate = LocalDate.now(),
        )

        val result = vehicleDebtsService.handle(request)

        verify {
            concessionariaService.createConcessionaria(any(), any(), any())
        }

        verify(exactly = 0) {
            fichaCompensacaoService.createFichaDeCompensacao(any(), any(), any())
        }

        result.shouldBeSuccess()
    }

    @ParameterizedTest
    @MethodSource("retryableBillResultFailures")
    fun `quando a validação de concessionaria falhar, não deve criar um pagamento`(failures: CreateBillResult.FAILURE) {
        every {
            concessionariaService.createConcessionaria(any(), any(), any())
        } returns failures

        val request = buildRequest(
            barcode = billAdded2.barcode!!,
            paymentStatus = VehicleDebtsBillRequest.Status.ACTIVE,
            dueDate = LocalDate.now(),
        )

        val result = vehicleDebtsService.handle(request)

        verify {
            concessionariaService.createConcessionaria(any(), any(), any())
        }

        verify(exactly = 0) {
            fichaCompensacaoService.createFichaDeCompensacao(any(), any(), any())
        }

        result.shouldBeFailure()
    }

    @ParameterizedTest
    @MethodSource("nonRetryableBillResultFailures")
    fun `quando a validação de ficha falhar e não for retentável, não deve criar um pagamento`(failures: CreateBillResult.FAILURE) {
        every {
            fichaCompensacaoService.createFichaDeCompensacao(any(), any(), any())
        } returns failures

        val request = buildRequest(
            barcode = billAddedFicha.barcode,
            paymentStatus = VehicleDebtsBillRequest.Status.ACTIVE,
        )

        val result = vehicleDebtsService.handle(request)

        verify {
            fichaCompensacaoService.createFichaDeCompensacao(any(), any(), any())
        }

        verify(exactly = 0) {
            concessionariaService.createConcessionaria(any(), any(), any())
        }

        result.shouldBeSuccess()
    }

    @ParameterizedTest
    @MethodSource("retryableBillResultFailures")
    fun `quando a validação de ficha falhar, não deve criar um pagamento`(failures: CreateBillResult.FAILURE) {
        every {
            fichaCompensacaoService.createFichaDeCompensacao(any(), any(), any())
        } returns failures

        val request = buildRequest(
            barcode = billAddedFicha.barcode,
            paymentStatus = VehicleDebtsBillRequest.Status.ACTIVE,
        )

        val result = vehicleDebtsService.handle(request)

        verify {
            fichaCompensacaoService.createFichaDeCompensacao(any(), any(), any())
        }

        verify(exactly = 0) {
            concessionariaService.createConcessionaria(any(), any(), any())
        }

        result.shouldBeFailure()
    }

    @Test
    fun `quando não vier data de vencimento para concessionaria, deve devolver erro`() {
        val request = buildRequest(
            paymentStatus = VehicleDebtsBillRequest.Status.ACTIVE,
            barcode = billAdded2.barcode!!,
        )

        val result = vehicleDebtsService.handle(request)

        result.shouldBeFailure()
    }

    @Test
    fun `quando vier um débito de concessionaria pago com barcode e data de vencimento, deve chamar a atualização de conta`() {
        val request = buildRequest(
            paymentStatus = VehicleDebtsBillRequest.Status.PAID,
            barcode = billAdded2.barcode!!,
            dueDate = LocalDate.now(),
        )

        every { concessionariaService.handlePaid(any()) } returns Result.success(billAdded2.billId)

        vehicleDebtsService.handle(request)
            .shouldBeSuccess()

        verify(exactly = 1) {
            concessionariaService.handlePaid(any())
        }
    }

    @Test
    fun `quando vier um débito de concessionaria pago com barcode e data de vencimento, atualizar o manual entry`() {
        val request = buildRequest(
            paymentStatus = VehicleDebtsBillRequest.Status.PAID,
            barcode = billAdded2.barcode!!,
            dueDate = LocalDate.now(),
            externalBillId = ExternalBillId("same-external", ExternalBillProvider.VEHICLE_DEBTS),
        )

        every { manualEntryService.findAllByWalletAndDueDate(any(), any()) } returns listOf(
            mockk<ManualEntry>(relaxed = true) {
                every { externalId } returns ExternalBillId("same-external", ExternalBillProvider.VEHICLE_DEBTS)
                every { id } returns ManualEntryId("id")
            },
        )

        every { concessionariaService.handlePaid(any()) } returns Result.success(billAdded2.billId)

        vehicleDebtsService.handle(request)

        verify(exactly = 1) {
            manualEntryService.ignore(ManualEntryId("id"), any())
        }
    }

    @Test
    fun `quando vier um débito de concessionaria pago com barcode sem data de vencimento, se existir a conta, deve atualizar a conta existente`() {
        val request = buildRequest(
            paymentStatus = VehicleDebtsBillRequest.Status.PAID,
            barcode = billAdded2.barcode!!,
        )

        every { concessionariaService.handlePaid(any()) } returns Result.success(billAdded2.billId)

        vehicleDebtsService.handle(request)
            .shouldBeSuccess()

        verify(exactly = 1) {
            concessionariaService.handlePaid(any())
        }
    }

    @Disabled
    @Test
    fun `quando vier um débito de concessionaria pago sem barcode e com dueDate, deve criar uma entry paga`() {
        val request = buildRequest(
            paymentStatus = VehicleDebtsBillRequest.Status.PAID,
            dueDate = LocalDate.now().plusDays(1),
        )

        every { manualEntryService.create(any(), any(), any(), any(), any(), any(), any(), any(), any(), any()) } returns mockk<ManualEntry>(relaxed = true).right()

        vehicleDebtsService.handle(request)
            .shouldBeSuccess()

        verify(exactly = 1) {
            manualEntryService.create(
                walletId = ACCOUNT.defaultWalletId(),
                title = "Teste multa",
                amount = 1,
                dueDate = LocalDate.now().plusDays(1),
                source = ofType<ActionSource.VehicleDebts>(),
                type = ManualEntryType.VEHICLE_DEBT,
                status = ManualEntryStatus.PAID,
                categoryId = any(),
                externalId = any(),
            )
        }
    }

    @Test
    fun `quando vier um débito de concessionaria pago sem barcode e sem dueDate, não deve fazer nada`() {
        val request = buildRequest(
            paymentStatus = VehicleDebtsBillRequest.Status.PAID,
        )

        vehicleDebtsService.handle(request)
            .shouldBeSuccess()

        verify(exactly = 0) {
            concessionariaService.handlePaid(any())
        }
    }

    @Test
    fun `quando vier uma conta paga com o mesmo id de uma existente, não deve criar uma nova`() {
        val request = buildRequest(
            paymentStatus = VehicleDebtsBillRequest.Status.PAID,
            externalBillId = ExternalBillId("same-external", ExternalBillProvider.VEHICLE_DEBTS),
            dueDate = LocalDate.now().plusDays(1),
        )

        every { manualEntryService.findAllByWalletAndDueDate(any(), any()) } returns listOf(
            mockk<ManualEntry> {
                every { externalId } returns ExternalBillId("same-external", ExternalBillProvider.VEHICLE_DEBTS)
            },
        )

        vehicleDebtsService.handle(request)
            .shouldBeSuccess()

        verify(exactly = 0) {
            manualEntryService.create(
                walletId = any(),
                title = any(),
                amount = any(),
                dueDate = any(),
                source = any(),
                type = any(),
                status = any(),
                categoryId = any(),
                externalId = any(),
            )
        }
    }

    @Disabled
    @Test
    fun `quando vier um notice com dueDate, deve criar uma entry`() {
        val request = buildRequest(
            paymentStatus = VehicleDebtsBillRequest.Status.NOTICE,
            dueDate = LocalDate.now().plusDays(1),
            externalBillId = ExternalBillId("teste", ExternalBillProvider.VEHICLE_DEBTS),
        )
        every { manualEntryService.create(any(), any(), any(), any(), any(), any(), any(), any(), any(), any()) } returns mockk<ManualEntry>(relaxed = true).right()

        vehicleDebtsService.handle(request)
            .shouldBeSuccess()

        verify(exactly = 1) {
            manualEntryService.create(
                walletId = ACCOUNT.defaultWalletId(),
                title = "Teste multa",
                amount = 1,
                dueDate = LocalDate.now().plusDays(1),
                source = ofType<ActionSource.VehicleDebts>(),
                type = ManualEntryType.VEHICLE_DEBT,
                status = ManualEntryStatus.ACTIVE,
                categoryId = any(),
                externalId = ExternalBillId("teste", ExternalBillProvider.VEHICLE_DEBTS),
            )
        }
    }

    @Test
    fun `quando vier um notice sem dueDate, não deve criar nada`() {
        val request = buildRequest(paymentStatus = VehicleDebtsBillRequest.Status.NOTICE)

        vehicleDebtsService.handle(request)
            .shouldBeSuccess()

        verify(exactly = 0) {
            manualEntryService.create(
                walletId = any(),
                title = any(),
                amount = any(),
                dueDate = any(),
                source = any(),
                type = any(),
                status = any(),
                categoryId = any(),
                externalId = any(),
            )
        }
    }

    @Test
    fun `quando vier um notice com o mesmo id, não deve criar um novo`() {
        val request = buildRequest(
            externalBillId = ExternalBillId("same-external", ExternalBillProvider.VEHICLE_DEBTS),
            dueDate = LocalDate.now().plusDays(1),
            paymentStatus = VehicleDebtsBillRequest.Status.NOTICE,
        )

        every { manualEntryService.findAllByWalletAndDueDate(any(), any()) } returns listOf(
            mockk<ManualEntry> {
                every { externalId } returns ExternalBillId("same-external", ExternalBillProvider.VEHICLE_DEBTS)
            },
        )

        vehicleDebtsService.handle(request)
            .shouldBeSuccess()

        verify(exactly = 0) {
            manualEntryService.create(
                walletId = any(),
                title = any(),
                amount = any(),
                dueDate = any(),
                source = any(),
                type = any(),
                status = any(),
                categoryId = any(),
                externalId = any(),
            )
        }
    }

    private fun buildRequest(barcode: BarCode? = null, dueDate: LocalDate? = null, externalBillId: ExternalBillId? = null, paymentStatus: VehicleDebtsBillRequest.Status) = VehicleDebtsBillRequest(
        accountId = ACCOUNT.accountId,
        barcode = barcode,
        description = "Teste multa",
        document = Document(value = "***********"),
        licensePlate = LicensePlate(value = "0000000"),
        dueDate = dueDate,
        amountTotal = 1,
        paymentStatus = paymentStatus,
        fees = 0,
        fine = 0,
        externalId = externalBillId?.value,
        type = VehicleDebtsType.Fine,
        unavailableReason = null,
        quota = null,
        identifier = null,
    )

    companion object {
        @JvmStatic
        @MethodSource("NonRetryableBillResultFailures")
        fun nonRetryableBillResultFailures() = listOf(
            CreateBillResult.FAILURE.AlreadyPaid.WithData(description = "Conta já paga", billRegisterData = billRegisterData),
            CreateBillResult.FAILURE.AlreadyPaid.WithoutData(description = "Conta já paga", barCode = BarCode.ofDigitable(CONCESSIONARIA_DIGITABLE_LINE), dueDate = getLocalDate()),
            CreateBillResult.FAILURE.BillAlreadyExists(Bill.build(billAdded)),

        )

        @JvmStatic
        @MethodSource("RetryableBillResultFailures")
        fun retryableBillResultFailures() = listOf(
            CreateBillResult.FAILURE.BillUnableToValidate(description = "Não foi possível validar a conta", isRetryable = true),
            CreateBillResult.FAILURE.ServerError(IllegalStateException()),
        )
    }
}