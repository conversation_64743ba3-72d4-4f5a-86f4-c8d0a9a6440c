package ai.friday.billpayment.app.caf

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.DocumentScan
import ai.friday.billpayment.app.documentscan.CAFDocumentScanPayloadData
import ai.friday.billpayment.app.documentscan.DocumentScanDigitalSpoofResult
import ai.friday.billpayment.app.documentscan.DocumentScanDigitalSpoofStatus
import ai.friday.billpayment.app.documentscan.DocumentScanFaceResult
import ai.friday.billpayment.app.documentscan.DocumentScanFaceStatus
import ai.friday.billpayment.app.documentscan.DocumentScanId
import ai.friday.billpayment.app.documentscan.DocumentScanImage
import ai.friday.billpayment.app.documentscan.DocumentScanOcrResult
import ai.friday.billpayment.app.documentscan.DocumentScanResult
import ai.friday.billpayment.app.documentscan.DocumentScanTextResult
import ai.friday.billpayment.app.documentscan.GetDocumentScanResultError
import ai.friday.billpayment.app.documentscan.GetImageError
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.CafAdapterInterface
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.liveness.LivenessEnrollmentVerification
import ai.friday.billpayment.app.liveness.LivenessErrors
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.liveness.LivenessMatchVerify
import ai.friday.billpayment.app.liveness.LivenessProvider
import ai.friday.billpayment.basicAccountRegisterData
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.ints.shouldBeGreaterThan
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldNotContain
import io.kotest.matchers.types.shouldBeInstanceOf
import io.micronaut.http.MutableHttpRequest
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import io.reactivex.Flowable
import java.time.LocalDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class CafServiceTest {

    private val cafAdapter = mockk<CafAdapterInterface>()
    val configuration = mockk<CafConfiguration>() {
        every { jwtSecret } returns "secret"
    }
    val accountRegisterRepository = mockk<AccountRegisterRepository>()
    val httpClient = mockk<RxHttpClient>()
    val messagePublisher = mockk<MessagePublisher>(relaxed = true)
    private val cafService = CafService(cafAdapter, accountRegisterRepository, configuration, httpClient, messagePublisher, "queue")

    private val referenceToken = "reference token"

    @Test
    fun `createTransaction should return success when adapter succeeds`() {
        val templateId = "template123"
        val files = listOf(
            FileData(data = "base64data", type = "SELFIE"),
            FileData(data = "base64document", type = "DOCUMENT_FRONT"),
        )
        val expectedResponse = CreateTransactionResponse(
            requestId = "request123",
            id = "transaction456",
            status = "PENDING",
        )

        every { cafAdapter.createTransaction(templateId, files, referenceToken) } returns expectedResponse.right()

        val result = cafService.createTransaction(templateId, files, referenceToken)

        result shouldBe expectedResponse.right()
        verify { cafAdapter.createTransaction(templateId, files, referenceToken) }
    }

    @Test
    fun `createTransaction should return error when adapter fails`() {
        val templateId = "template123"
        val files = listOf(FileData(data = "base64data", type = "SELFIE"))
        val expectedException = RuntimeException("CAF API error")

        every { cafAdapter.createTransaction(templateId, files, referenceToken) } returns expectedException.left()

        val result = cafService.createTransaction(templateId, files, referenceToken)

        result shouldBe expectedException.left()
        verify { cafAdapter.createTransaction(templateId, files, referenceToken) }
    }

    @Test
    fun `listTransactions should return success when adapter succeeds`() {
        val expectedResponse = ListTransactionsResponse(
            requestId = "request123",
            items = listOf(
                TransactionItem(
                    id = "transaction1",
                    status = "APPROVED",
                    createdAt = LocalDateTime.of(2023, 12, 1, 10, 0),
                    data = TransactionData(
                        cpf = "12345678901",
                        birthDate = "1990-01-01",
                        name = "João Silva",
                    ),
                ),
                TransactionItem(
                    id = "transaction2",
                    status = "REJECTED",
                    createdAt = LocalDateTime.of(2023, 12, 2, 11, 0),
                    data = null,
                ),
            ),
            totalItems = 2,
        )

        every { cafAdapter.listTransactions() } returns expectedResponse.right()

        val result = cafService.listTransactions()

        result shouldBe expectedResponse.right()
        verify { cafAdapter.listTransactions() }
    }

    @Test
    fun `listTransactions should return error when adapter fails`() {
        val expectedException = RuntimeException("CAF API error")

        every { cafAdapter.listTransactions() } returns expectedException.left()

        val result = cafService.listTransactions()

        result shouldBe expectedException.left()
        verify { cafAdapter.listTransactions() }
    }

    @Test
    fun `getTransaction should return success when adapter succeeds`() {
        val transactionId = "transaction123"
        val expectedResponse = GetTransactionResponse(
            requestId = "request123",
            id = "transaction123",
            status = "APPROVED",
            type = null,
            images = null,
            sections = null,
            history = null,
            metadata = null,
            templateId = null,
            createdAt = null,
            fraud = null,
            fraudScore = null,
            statusReasons = null,
            attributes = null,
            files = null,
        )

        every { cafAdapter.getTransaction(transactionId) } returns expectedResponse.right()

        val result = cafService.getTransaction(transactionId)

        result shouldBe expectedResponse.right()
        verify { cafAdapter.getTransaction(transactionId) }
    }

    @Test
    fun `getTransaction should return error when adapter fails`() {
        val transactionId = "transaction123"
        val expectedException = RuntimeException("Transaction not found")

        every { cafAdapter.getTransaction(transactionId) } returns expectedException.left()

        val result = cafService.getTransaction(transactionId)

        result shouldBe expectedException.left()
        verify { cafAdapter.getTransaction(transactionId) }
    }

    @Test
    fun `createTransaction should handle empty files list`() {
        val templateId = "template123"
        val files = emptyList<FileData>()
        val expectedResponse = CreateTransactionResponse(
            requestId = "request123",
            id = "transaction456",
            status = "PENDING",
        )

        every { cafAdapter.createTransaction(templateId, files, referenceToken) } returns expectedResponse.right()

        val result = cafService.createTransaction(templateId, files, referenceToken)

        result shouldBe expectedResponse.right()
        verify { cafAdapter.createTransaction(templateId, files, referenceToken) }
    }

    @Test
    fun `createTransaction should handle different file types`() {
        val templateId = "template123"
        val files = listOf(
            FileData(data = "selfie_data", type = "SELFIE"),
            FileData(data = "document_front_data", type = "DOCUMENT_FRONT"),
            FileData(data = "document_back_data", type = "DOCUMENT_BACK"),
        )
        val expectedResponse = CreateTransactionResponse(
            requestId = "request123",
            id = "transaction456",
            status = "PENDING",
        )

        every { cafAdapter.createTransaction(templateId, files, referenceToken) } returns expectedResponse.right()

        val result = cafService.createTransaction(templateId, files, referenceToken)

        result shouldBe expectedResponse.right()
        verify { cafAdapter.createTransaction(templateId, files, referenceToken) }
    }

    @Test
    fun `listTransactions should handle empty results`() {
        val expectedResponse = ListTransactionsResponse(
            requestId = "request123",
            items = emptyList(),
            totalItems = 0,
        )

        every { cafAdapter.listTransactions() } returns expectedResponse.right()

        val result = cafService.listTransactions()

        result shouldBe expectedResponse.right()
        verify { cafAdapter.listTransactions() }
    }

    @Test
    fun `getTransaction should handle different transaction statuses`() {
        val transactionId = "transaction123"
        val statuses = listOf("PENDING", "APPROVED", "REJECTED")

        statuses.forEach { status ->
            val expectedResponse = GetTransactionResponse(
                requestId = "request123",
                id = transactionId,
                status = status,
                type = null,
                images = null,
                sections = null,
                history = null,
                metadata = null,
                templateId = null,
                createdAt = null,
                fraud = null,
                fraudScore = null,
                statusReasons = null,
                attributes = null,
                files = null,
            )

            every { cafAdapter.getTransaction(transactionId) } returns expectedResponse.right()

            val result = cafService.getTransaction(transactionId)

            result shouldBe expectedResponse.right()
        }

        verify(exactly = statuses.size) { cafAdapter.getTransaction(transactionId) }
    }

    @Nested
    inner class VerifyDuplication {

        private fun baseSections() = Sections(
            liveness = null,
            facematch = null,
            officialData = null,
            document = null,
            documentscopy = null,
            facialbiometrics = null,
            privateFaceset = null,
            sharedFaceset = null,
            documentLiveness = null,
            globalDocumentVerification = null,
            ocr = null,
            pfCpfData = null,
            cpf = null,
        )

        private fun baseTransactionResponse(sections: Sections? = null) = GetTransactionResponse(
            requestId = "request123",
            id = "transaction123",
            status = "APPROVED",
            type = null,
            images = null,
            sections = sections,
            history = null,
            metadata = null,
            templateId = null,
            createdAt = null,
            fraud = null,
            fraudScore = null,
            statusReasons = null,
            attributes = null,
            files = null,
        )

        @Test
        fun `should return empty verification when no livenessId exists`() {
            val accountId = AccountId("account123")
            val accountRegister = mockk<AccountRegisterData> {
                every { livenessId } returns null
            }

            every { accountRegisterRepository.findByAccountId(accountId) } returns accountRegister

            val result = cafService.verifyDuplication(accountId)

            result shouldBe LivenessEnrollmentVerification().right()
            verify { accountRegisterRepository.findByAccountId(accountId) }
        }

        @Test
        fun `should return empty verification when livenessId provider is not CAF`() {
            val accountId = AccountId("account123")
            val livenessId = LivenessId("liveness123", LivenessProvider.FACETEC)
            val accountRegister = basicAccountRegisterData.copy(livenessId = livenessId)

            every { accountRegisterRepository.findByAccountId(accountId) } returns accountRegister

            val result = cafService.verifyDuplication(accountId)

            result shouldBe LivenessEnrollmentVerification().right()
            verify { accountRegisterRepository.findByAccountId(accountId) }
        }

        @Test
        fun `should return error when adapter fails to get transaction`() {
            val accountId = AccountId("account123")
            val livenessId = LivenessId("liveness123", LivenessProvider.CAF)
            val accountRegister = basicAccountRegisterData.copy(livenessId = livenessId)
            val exception = RuntimeException("Transaction not found")

            every { accountRegisterRepository.findByAccountId(accountId) } returns accountRegister
            every { cafAdapter.getTransaction(livenessId.value) } returns exception.left()

            val result = cafService.verifyDuplication(accountId)

            result shouldBe LivenessErrors.Error(exception).left()
            verify { accountRegisterRepository.findByAccountId(accountId) }
            verify { cafAdapter.getTransaction(livenessId.value) }
        }

        @Test
        fun `should return empty verification when transaction has no sections`() {
            val accountId = AccountId("account123")
            val livenessId = LivenessId("liveness123", LivenessProvider.CAF)
            val accountRegister = basicAccountRegisterData.copy(livenessId = livenessId)
            val transactionResponse = baseTransactionResponse()

            every { accountRegisterRepository.findByAccountId(accountId) } returns accountRegister
            every { cafAdapter.getTransaction(livenessId.value) } returns transactionResponse.right()

            val result = cafService.verifyDuplication(accountId)

            result shouldBe LivenessEnrollmentVerification().right()
            verify { accountRegisterRepository.findByAccountId(accountId) }
            verify { cafAdapter.getTransaction(livenessId.value) }
        }

        @Test
        fun `should detect fraud from privateFaceset when suspect is true`() {
            val accountId = AccountId("account123")
            val livenessId = LivenessId("liveness123", LivenessProvider.CAF)
            val accountRegister = basicAccountRegisterData.copy(livenessId = livenessId)
            val sections = baseSections().copy(
                privateFaceset = PrivateFacesetSection(
                    statusCode = "200",
                    data = PrivateFacesetData(
                        suspect = true,
                        faceMatches = listOf(
                            PrivateFaceMatch(
                                similarity = "0.95",
                                verified = true,
                                faceset = FacesetInfo(
                                    id = "faceset123",
                                    createdAt = "2023-01-01",
                                    description = "Test faceset",
                                ),
                            ),
                        ),
                    ),
                ),
            )
            val transactionResponse = baseTransactionResponse(sections)

            every { accountRegisterRepository.findByAccountId(accountId) } returns accountRegister
            every { cafAdapter.getTransaction(livenessId.value) } returns transactionResponse.right()

            val result = cafService.verifyDuplication(accountId)

            val expectedVerification = LivenessEnrollmentVerification(
                duplications = LivenessEnrollmentVerification.Result.create(emptyList()),
                fraudIndications = LivenessEnrollmentVerification.Result.create(listOf(accountId)),
            )

            result shouldBe expectedVerification.right()
            verify { accountRegisterRepository.findByAccountId(accountId) }
            verify { cafAdapter.getTransaction(livenessId.value) }
        }

        @Test
        fun `should detect fraud from sharedFaceset when suspect is true`() {
            val accountId = AccountId("account123")
            val livenessId = LivenessId("liveness123", LivenessProvider.CAF)
            val accountRegister = basicAccountRegisterData.copy(livenessId = livenessId)
            val sections = baseSections().copy(
                sharedFaceset = SharedFacesetSection(
                    statusCode = "200",
                    data = SharedFacesetData(
                        suspect = true,
                        faceMatches = listOf(
                            SharedFaceMatch(
                                similarity = "0.92",
                                verified = true,
                            ),
                        ),
                    ),
                ),
            )
            val transactionResponse = baseTransactionResponse(sections)

            every { accountRegisterRepository.findByAccountId(accountId) } returns accountRegister
            every { cafAdapter.getTransaction(livenessId.value) } returns transactionResponse.right()

            val result = cafService.verifyDuplication(accountId)

            val expectedVerification = LivenessEnrollmentVerification(
                duplications = LivenessEnrollmentVerification.Result.create(emptyList()),
                fraudIndications = LivenessEnrollmentVerification.Result.create(listOf(accountId)),
            )

            result shouldBe expectedVerification.right()
            verify { accountRegisterRepository.findByAccountId(accountId) }
            verify { cafAdapter.getTransaction(livenessId.value) }
        }

        @Test
        fun `should detect fraud from both privateFaceset and sharedFaceset`() {
            val accountId = AccountId("account123")
            val livenessId = LivenessId("liveness123", LivenessProvider.CAF)
            val accountRegister = basicAccountRegisterData.copy(livenessId = livenessId)
            val sections = baseSections().copy(
                privateFaceset = PrivateFacesetSection(
                    statusCode = "200",
                    data = PrivateFacesetData(
                        suspect = true,
                        faceMatches = listOf(
                            PrivateFaceMatch(
                                similarity = "0.95",
                                verified = true,
                                faceset = FacesetInfo(
                                    id = "faceset123",
                                    createdAt = "2023-01-01",
                                    description = "Test faceset",
                                ),
                            ),
                        ),
                    ),
                ),
                sharedFaceset = SharedFacesetSection(
                    statusCode = "200",
                    data = SharedFacesetData(
                        suspect = true,
                        faceMatches = listOf(
                            SharedFaceMatch(
                                similarity = "0.92",
                                verified = true,
                            ),
                        ),
                    ),
                ),
            )
            val transactionResponse = baseTransactionResponse(sections)

            every { accountRegisterRepository.findByAccountId(accountId) } returns accountRegister
            every { cafAdapter.getTransaction(livenessId.value) } returns transactionResponse.right()

            val result = cafService.verifyDuplication(accountId)

            val expectedVerification = LivenessEnrollmentVerification(
                duplications = LivenessEnrollmentVerification.Result.create(emptyList()),
                fraudIndications = LivenessEnrollmentVerification.Result.create(listOf(accountId)),
            )

            result shouldBe expectedVerification.right()
            verify { accountRegisterRepository.findByAccountId(accountId) }
            verify { cafAdapter.getTransaction(livenessId.value) }
        }

        @Test
        fun `should not detect fraud when suspect is false`() {
            val accountId = AccountId("account123")
            val livenessId = LivenessId("liveness123", LivenessProvider.CAF)
            val accountRegister = basicAccountRegisterData.copy(livenessId = livenessId)
            val sections = baseSections().copy(
                privateFaceset = PrivateFacesetSection(
                    statusCode = "200",
                    data = PrivateFacesetData(
                        suspect = false,
                        faceMatches = emptyList(),
                    ),
                ),
                sharedFaceset = SharedFacesetSection(
                    statusCode = "200",
                    data = SharedFacesetData(
                        suspect = false,
                        faceMatches = emptyList(),
                    ),
                ),
            )
            val transactionResponse = baseTransactionResponse(sections)

            every { accountRegisterRepository.findByAccountId(accountId) } returns accountRegister
            every { cafAdapter.getTransaction(livenessId.value) } returns transactionResponse.right()

            val result = cafService.verifyDuplication(accountId)

            val expectedVerification = LivenessEnrollmentVerification(
                duplications = LivenessEnrollmentVerification.Result.create(emptyList()),
                fraudIndications = LivenessEnrollmentVerification.Result.create(emptyList()),
            )

            result shouldBe expectedVerification.right()
            verify { accountRegisterRepository.findByAccountId(accountId) }
            verify { cafAdapter.getTransaction(livenessId.value) }
        }

        @Test
        fun `should handle exception and return error`() {
            val accountId = AccountId("account123")
            val exception = RuntimeException("Repository error")

            every { accountRegisterRepository.findByAccountId(accountId) } throws exception

            val result = cafService.verifyDuplication(accountId)

            result shouldBe LivenessErrors.Error(exception).left()
            verify { accountRegisterRepository.findByAccountId(accountId) }
        }
    }

    @DisplayName("Create Document")
    @Nested
    inner class CreateDocument {

        @BeforeEach
        fun init() {
            every { configuration.documentLivenessTemplateId } returns "documentLivenessTemplateId"
            every { cafAdapter.createTransaction(any(), any(), any(), any(), any(), any()) } returns CreateTransactionResponse(
                requestId = "requestId",
                id = "documentLivenessTemplateIdTransactionId",
                status = "PENDING",
            ).right()
            every { accountRegisterRepository.findByAccountId(ACCOUNT.accountId) } returns basicAccountRegisterData
            every { accountRegisterRepository.save(any()) } answers { firstArg() }
        }

        @Test
        fun `deve processar corretamente e atualizar o register data`() {
            val jwt =
                """eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjYXB0dXJlcyI6W3sic2Nhbm5lZExhYmVsIjoicmdfZnJvbnQiLCJpbWFnZVVybCI6Imh0dHBzOi8vc2Rrcy1hcGktcHJvZC11cy1lYXN0LTEtdXBsb2Fkcy12MS5zMy51cy1lYXN0LTEuYW1hem9uYXdzLmNvbS90ZW1wL2NlM2U3ODIxLTNiZjUtNGUzOS04MzYwLTRkYzAxNjllYTI3Ny91cGxvYWRfMTc1NTcyMzQ0MDA3OV9kZmQ1NTM0ZS5qcGc_******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.oOeOU5hU3q6TOUQQYKcvgvxQY3E5I8sGegxvAWM6W6k"""
            val result = cafService.upsertDocumentScanId(
                CAFDocumentScanPayloadData(
                    accountId = ACCOUNT.accountId,
                    jwt = jwt,
                    referenceToken = "referenceToken",
                ),
            )

            result.getOrNull() shouldBe DocumentScan(
                documentType = DocumentType.RG,
                documentScanId = DocumentScanId("documentLivenessTemplateIdTransactionId", LivenessProvider.CAF, "referenceToken", null),
            )
            val accountRegisterDataCaptor = slot<AccountRegisterData>()
            verify {
                accountRegisterRepository.save(capture(accountRegisterDataCaptor))
            }

            with(accountRegisterDataCaptor.captured) {
                documentScan.shouldNotBeNull()
                documentScan!!.documentScanId shouldBe DocumentScanId("documentLivenessTemplateIdTransactionId", LivenessProvider.CAF, "referenceToken", null)
                documentScan!!.documentType shouldBe DocumentType.RG
            }
        }
    }

    @DisplayName("GetResult")
    @Nested
    inner class GetResult {

        @Test
        fun `should return complete DocumentScanResult when transaction is approved`() {
            val documentScanId = DocumentScanId("68b04f1bfcbae60002a58e37", LivenessProvider.CAF, "test-reference-token", null)
            val transactionResponse = parseObjectFrom<GetTransactionResponse>(parsedDocumentResult)

            every { cafAdapter.getTransaction(documentScanId.value) } returns transactionResponse.right()

            val result = cafService.getResult(documentScanId)

            result shouldBe DocumentScanResult(
                digitalSpoof = DocumentScanDigitalSpoofResult(DocumentScanDigitalSpoofStatus.LIKELY_PHYSICAL_ID),
                face = DocumentScanFaceResult.Found(DocumentScanFaceStatus.LIKELY_ORIGINAL_FACE),
                text = DocumentScanTextResult.NotPerformed,
                ocr = DocumentScanOcrResult.Matched,
            ).right()

            verify { cafAdapter.getTransaction(documentScanId.value) }
        }

        @Test
        fun `should return incomplete error when transaction status is PROCESSING`() {
            val documentScanId = DocumentScanId("68b04f1bfcbae60002a58e37", LivenessProvider.CAF, "test-reference-token", null)
            val transactionResponse = parseObjectFrom<GetTransactionResponse>(parsedDocumentResult).copy(status = "PROCESSING")

            every { cafAdapter.getTransaction(documentScanId.value) } returns transactionResponse.right()

            val result = cafService.getResult(documentScanId)

            result shouldBe GetDocumentScanResultError.Incomplete(documentScanId).left()
            verify { cafAdapter.getTransaction(documentScanId.value) }
        }

        @Test
        fun `should return error when transaction has no sections`() {
            val documentScanId = DocumentScanId("68b04f1bfcbae60002a58e37", LivenessProvider.CAF, "test-reference-token", null)
            val transactionResponse = parseObjectFrom<GetTransactionResponse>(parsedDocumentResult).copy(
                status = "APPROVED",
                sections = null,
            )

            every { cafAdapter.getTransaction(documentScanId.value) } returns transactionResponse.right()

            val result = cafService.getResult(documentScanId)

            result.isLeft() shouldBe true
            result.leftOrNull().shouldBeInstanceOf<GetDocumentScanResultError.Unexpected>()
            verify { cafAdapter.getTransaction(documentScanId.value) }
        }

        @Test
        fun `should return error when adapter fails`() {
            val documentScanId = DocumentScanId("68b04f1bfcbae60002a58e37", LivenessProvider.CAF, "test-reference-token", null)
            val exception = RuntimeException("CAF API error")

            every { cafAdapter.getTransaction(documentScanId.value) } returns exception.left()

            val result = cafService.getResult(documentScanId)

            result shouldBe GetDocumentScanResultError.Unexpected(exception).left()
            verify { cafAdapter.getTransaction(documentScanId.value) }
        }

        @Test
        fun `should handle documentLiveness with statusCode not 01`() {
            val documentScanId = DocumentScanId("68b04f1bfcbae60002a58e37", LivenessProvider.CAF, "test-reference-token", null)
            val transactionResponse = parseObjectFrom<GetTransactionResponse>(parsedDocumentResult).copy(
                sections = parseObjectFrom<GetTransactionResponse>(parsedDocumentResult).sections!!.copy(
                    documentLiveness = DocumentLivenessSection(
                        statusCode = "00", // Not successful
                        data = null,
                    ),
                ),
            )

            every { cafAdapter.getTransaction(documentScanId.value) } returns transactionResponse.right()

            val result = cafService.getResult(documentScanId)

            result.map { it.digitalSpoof.status } shouldBe DocumentScanDigitalSpoofStatus.CANNOT_CONFIRM_PHYSICAL_ID.right()
            verify { cafAdapter.getTransaction(documentScanId.value) }
        }

        @Test
        fun `should handle documentscopy with fraud detected`() {
            val documentScanId = DocumentScanId("68b04f1bfcbae60002a58e37", LivenessProvider.CAF, "test-reference-token", null)
            val transactionResponse = parseObjectFrom<GetTransactionResponse>(parsedDocumentResult).copy(
                sections = parseObjectFrom<GetTransactionResponse>(parsedDocumentResult).sections!!.copy(
                    documentscopy = DocumentscopySection(
                        status = "APPROVED",
                        fraud = true, // Fraud detected
                        evaluation = emptyList(),
                        reviewDate = "2025-08-28T12:44:48.014Z",
                    ),
                ),
            )

            every { cafAdapter.getTransaction(documentScanId.value) } returns transactionResponse.right()

            val result = cafService.getResult(documentScanId)

            result.map { it.face } shouldBe DocumentScanFaceResult.Found(DocumentScanFaceStatus.CANNOT_CONFIRM_ID_IS_AUTHENTIC).right()
            verify { cafAdapter.getTransaction(documentScanId.value) }
        }

        @Test
        fun `should handle cpf and pfCpfData sections for OCR matching`() {
            val documentScanId = DocumentScanId("68b04f1bfcbae60002a58e37", LivenessProvider.CAF, "test-reference-token", null)
            val transactionResponse = parseObjectFrom<GetTransactionResponse>(parsedDocumentResult).copy(
                sections = parseObjectFrom<GetTransactionResponse>(parsedDocumentResult).sections!!.copy(
                    pfCpfData = PfCpfDataSection(statusCode = "00", data = null), // Failed
                    cpf = CpfSection(statusCode = "01", registrationStatusCode = null, registrationStatusMessage = null, name = null, socialName = null, birthDate = null, issueDate = null, deathYear = null, deathMessage = null), // Failed
                ),
            )

            every { cafAdapter.getTransaction(documentScanId.value) } returns transactionResponse.right()

            val result = cafService.getResult(documentScanId)

            result.map { it.ocr } shouldBe DocumentScanOcrResult.NotMatched.right()
            verify { cafAdapter.getTransaction(documentScanId.value) }
        }
    }

    @DisplayName("GetImage")
    @Nested
    inner class GetImage {

        @Test
        fun `should return incomplete error when transaction status is PROCESSING`() {
            val documentScanId = DocumentScanId("68b04f1bfcbae60002a58e37", LivenessProvider.CAF, "test-reference-token", null)
            val transactionResponse = parseObjectFrom<GetTransactionResponse>(parsedDocumentResult).copy(status = "PROCESSING")

            every { cafAdapter.getTransaction(documentScanId.value) } returns transactionResponse.right()

            val result = cafService.getImage(documentScanId)

            result shouldBe GetImageError.Incomplete(documentScanId).left()
            verify { cafAdapter.getTransaction(documentScanId.value) }
        }

        @Test
        fun `should return error when transaction has no images`() {
            val documentScanId = DocumentScanId("68b04f1bfcbae60002a58e37", LivenessProvider.CAF, "test-reference-token", null)
            val transactionResponse = parseObjectFrom<GetTransactionResponse>(parsedDocumentResult).copy(
                status = "APPROVED",
                images = null,
            )

            every { cafAdapter.getTransaction(documentScanId.value) } returns transactionResponse.right()

            val result = cafService.getImage(documentScanId)

            result.isLeft() shouldBe true
            result.leftOrNull().shouldBeInstanceOf<GetImageError.Unexpected>()
            verify { cafAdapter.getTransaction(documentScanId.value) }
        }

        @Test
        fun `should return error when no document images found`() {
            val documentScanId = DocumentScanId("68b04f1bfcbae60002a58e37", LivenessProvider.CAF, "test-reference-token", null)
            val transactionResponse = parseObjectFrom<GetTransactionResponse>(parsedDocumentResult).copy(
                images = Images(
                    selfie = "https://example.com/selfie.jpg",
                    front = null, // No document images
                    back = null,
                ),
            )

            every { cafAdapter.getTransaction(documentScanId.value) } returns transactionResponse.right()

            val result = cafService.getImage(documentScanId)

            result.isLeft() shouldBe true
            result.leftOrNull().shouldBeInstanceOf<GetImageError.Unexpected>()
            verify { cafAdapter.getTransaction(documentScanId.value) }
        }

        @Test
        fun `should return error when adapter fails`() {
            val documentScanId = DocumentScanId("68b04f1bfcbae60002a58e37", LivenessProvider.CAF, "test-reference-token", null)
            val exception = RuntimeException("CAF API error")

            every { cafAdapter.getTransaction(documentScanId.value) } returns exception.left()

            val result = cafService.getImage(documentScanId)

            result shouldBe GetImageError.Unexpected(exception).left()
            verify { cafAdapter.getTransaction(documentScanId.value) }
        }

        @Test
        fun `should proceed to image download when transaction is approved with images`() {
            val documentScanId = DocumentScanId("68b04f1bfcbae60002a58e37", LivenessProvider.CAF, "test-reference-token", null)
            val transactionResponse = parseObjectFrom<GetTransactionResponse>(parsedDocumentResult)
            val frontImageBytes = "BYTES DA FRENTE".toByteArray()
            val backImageBytes = "BYTES DO VERSO".toByteArray()

            every { cafAdapter.getTransaction(documentScanId.value) } returns transactionResponse.right()

            every {
                httpClient.retrieve(any<MutableHttpRequest<*>>(), ByteArray::class.java)
            } returns Flowable.just(frontImageBytes) andThen Flowable.just(backImageBytes)

            val result = cafService.getImage(documentScanId)

            result.isRight() shouldBe true
            val imageResult = result.getOrNull()!!
            imageResult.shouldBeInstanceOf<DocumentScanImage.DoubleSided>()

            val doubleSided = imageResult
            doubleSided.front.content shouldBe frontImageBytes
            doubleSided.front.extension shouldBe "jpg"
            doubleSided.back.content shouldBe backImageBytes
            doubleSided.back.extension shouldBe "jpg"

            verify { cafAdapter.getTransaction(documentScanId.value) }
            verify(exactly = 2) { httpClient.retrieve(any<MutableHttpRequest<*>>(), ByteArray::class.java) }
        }

        @Test
        fun `should return SingleSided image when only front URL exists`() {
            val documentScanId = DocumentScanId("68b04f1bfcbae60002a58e37", LivenessProvider.CAF, "test-reference-token", null)
            val transactionResponse = parseObjectFrom<GetTransactionResponse>(parsedDocumentResult).copy(
                images = Images(
                    selfie = null,
                    front = "https://example.com/front.jpg",
                    back = null, // Only front image
                ),
            )
            val frontImageBytes = "BYTES DA FRENTE".toByteArray()

            every { cafAdapter.getTransaction(documentScanId.value) } returns transactionResponse.right()
            every {
                httpClient.retrieve(any<MutableHttpRequest<*>>(), ByteArray::class.java)
            } returns Flowable.just(frontImageBytes)

            val result = cafService.getImage(documentScanId)

            result.isRight() shouldBe true
            val imageResult = result.getOrNull()!!
            imageResult.shouldBeInstanceOf<DocumentScanImage.SingleSided>()

            val singleSided = imageResult as DocumentScanImage.SingleSided
            singleSided.content.content shouldBe frontImageBytes
            singleSided.content.extension shouldBe "jpg"

            verify { cafAdapter.getTransaction(documentScanId.value) }
            verify(exactly = 1) { httpClient.retrieve(any<MutableHttpRequest<*>>(), ByteArray::class.java) }
        }

        @Test
        fun `should return error when image download fails`() {
            val documentScanId = DocumentScanId("68b04f1bfcbae60002a58e37", LivenessProvider.CAF, "test-reference-token", null)
            val transactionResponse = parseObjectFrom<GetTransactionResponse>(parsedDocumentResult)
            val downloadException = RuntimeException("Download failed")

            every { cafAdapter.getTransaction(documentScanId.value) } returns transactionResponse.right()
            every {
                httpClient.retrieve(any<MutableHttpRequest<*>>(), ByteArray::class.java)
            } returns Flowable.error(downloadException)

            val result = cafService.getImage(documentScanId)

            result.isLeft() shouldBe true
            result.leftOrNull().shouldBeInstanceOf<GetImageError.Unexpected>()
            verify { cafAdapter.getTransaction(documentScanId.value) }
            verify(exactly = 1) { httpClient.retrieve(any<MutableHttpRequest<*>>(), ByteArray::class.java) }
        }
    }

    @DisplayName("GenerateJwtToken")
    @Nested
    inner class GenerateJwtToken {

        @Test
        fun `should generate valid JWT token with correct structure`() {
            val payload = CAFJwtPayload(
                iss = "friday-bill-payment-service",
                exp = 1640995200L, // 2022-01-01 00:00:00 UTC
            )
            val secret = "test-secret-key"

            val result = cafService.generateJwtToken(payload, secret)

            // JWT deve ter 3 partes separadas por pontos
            val parts = result.split(".")
            parts.size shouldBe 3

            // Verificar que o resultado não está vazio
            result.shouldNotBeNull()
            result.length shouldBeGreaterThan 0

            // Verificar que não contém caracteres não-URL-safe
            result shouldNotContain "="
            result shouldNotContain "+"
            result shouldNotContain "/"

            println(result)
        }
    }

    @DisplayName("RegisterFace")
    @Nested
    inner class RegisterFace {

        @Test
        fun `should return TransactionNotFound when accountRegister has no livenessId`() {
            val accountId = AccountId("account123")
            val imageUrl = "https://example.com/image.jpg"
            val accountRegister = basicAccountRegisterData.copy(livenessId = null)

            every { accountRegisterRepository.findByAccountId(accountId) } returns accountRegister

            val result = cafService.registerFace(accountId, imageUrl)

            result shouldBe FaceMatchError.TransactionNotFound.left()
            verify { accountRegisterRepository.findByAccountId(accountId) }
        }

        @Test
        fun `should return TransactionNotFound when livenessId provider is not CAF`() {
            val accountId = AccountId("account123")
            val imageUrl = "https://example.com/image.jpg"
            val livenessId = LivenessId("liveness123", LivenessProvider.FACETEC)
            val accountRegister = basicAccountRegisterData.copy(livenessId = livenessId)

            every { accountRegisterRepository.findByAccountId(accountId) } returns accountRegister

            val result = cafService.registerFace(accountId, imageUrl)

            result shouldBe FaceMatchError.TransactionNotFound.left()
            verify { accountRegisterRepository.findByAccountId(accountId) }
        }

        @Test
        fun `should return TransactionProcessing when transaction status is PROCESSING`() {
            val accountId = AccountId("account123")
            val imageUrl = "https://example.com/image.jpg"
            val livenessId = LivenessId("liveness123", LivenessProvider.CAF)
            val accountRegister = basicAccountRegisterData.copy(livenessId = livenessId)
            val transactionResponse = GetTransactionResponse(
                requestId = "request123",
                id = "liveness123",
                status = "PROCESSING",
                type = null,
                images = null,
                sections = null,
                history = null,
                metadata = null,
                templateId = null,
                createdAt = null,
                fraud = null,
                fraudScore = null,
                statusReasons = null,
                attributes = null,
                files = null,
            )

            every { accountRegisterRepository.findByAccountId(accountId) } returns accountRegister
            every { cafAdapter.getTransaction(livenessId.value) } returns transactionResponse.right()

            val result = cafService.registerFace(accountId, imageUrl)

            result shouldBe FaceMatchError.TransactionProcessing.left()
            verify { accountRegisterRepository.findByAccountId(accountId) }
            verify { cafAdapter.getTransaction(livenessId.value) }
        }

        @Test
        fun `should register face successfully when transaction is approved`() {
            val accountId = AccountId("account123")
            val imageUrl = "https://example.com/image.jpg"
            val livenessId = LivenessId("liveness123", LivenessProvider.CAF)
            val accountRegister = basicAccountRegisterData.copy(livenessId = livenessId)
            val transactionResponse = GetTransactionResponse(
                requestId = "request123",
                id = "liveness123",
                status = "APPROVED",
                type = null,
                images = null,
                sections = null,
                history = null,
                metadata = null,
                templateId = null,
                createdAt = null,
                fraud = null,
                fraudScore = null,
                statusReasons = null,
                attributes = null,
                files = null,
            )

            every { accountRegisterRepository.findByAccountId(accountId) } returns accountRegister
            every { cafAdapter.getTransaction(livenessId.value) } returns transactionResponse.right()
            every { cafAdapter.registerFace(accountId.value, imageUrl) } returns Unit.right()

            val result = cafService.registerFace(accountId, imageUrl)

            result shouldBe Unit.right()
            verify { accountRegisterRepository.findByAccountId(accountId) }
            verify { cafAdapter.getTransaction(livenessId.value) }
            verify { cafAdapter.registerFace(accountId.value, imageUrl) }
        }

        @Test
        fun `should return error when adapter fails to get transaction`() {
            val accountId = AccountId("account123")
            val imageUrl = "https://example.com/image.jpg"
            val livenessId = LivenessId("liveness123", LivenessProvider.CAF)
            val accountRegister = basicAccountRegisterData.copy(livenessId = livenessId)
            val exception = RuntimeException("Transaction not found")

            every { accountRegisterRepository.findByAccountId(accountId) } returns accountRegister
            every { cafAdapter.getTransaction(livenessId.value) } returns exception.left()

            val result = cafService.registerFace(accountId, imageUrl)

            result shouldBe FaceMatchError.Error(exception).left()
            verify { accountRegisterRepository.findByAccountId(accountId) }
            verify { cafAdapter.getTransaction(livenessId.value) }
        }

        @Test
        fun `should return error when adapter fails to register face`() {
            val accountId = AccountId("account123")
            val imageUrl = "https://example.com/image.jpg"
            val livenessId = LivenessId("liveness123", LivenessProvider.CAF)
            val accountRegister = basicAccountRegisterData.copy(livenessId = livenessId)
            val transactionResponse = GetTransactionResponse(
                requestId = "request123",
                id = "liveness123",
                status = "APPROVED",
                type = null,
                images = null,
                sections = null,
                history = null,
                metadata = null,
                templateId = null,
                createdAt = null,
                fraud = null,
                fraudScore = null,
                statusReasons = null,
                attributes = null,
                files = null,
            )
            val exception = RuntimeException("CAF API error")

            every { accountRegisterRepository.findByAccountId(accountId) } returns accountRegister
            every { cafAdapter.getTransaction(livenessId.value) } returns transactionResponse.right()
            every { cafAdapter.registerFace(accountId.value, imageUrl) } returns exception.left()

            val result = cafService.registerFace(accountId, imageUrl)

            result shouldBe FaceMatchError.Error(exception).left()
            verify { accountRegisterRepository.findByAccountId(accountId) }
            verify { cafAdapter.getTransaction(livenessId.value) }
            verify { cafAdapter.registerFace(accountId.value, imageUrl) }
        }
    }

    @DisplayName("VerifyMatch")
    @Nested
    inner class VerifyMatch {

        @Test
        fun `should return MatchUnavailable when matchLivenessId is null`() {
            val livenessId = LivenessId("liveness123", LivenessProvider.CAF)

            val result = cafService.verifyMatch(livenessId)

            result shouldBe LivenessErrors.MatchUnavailable.left()
        }

        @Test
        fun `should verify match successfully when face authentication is successful`() {
            val accountId = AccountId("account123")
            val matchLivenessId = "attempt456"
            val livenessId = LivenessId("liveness123", LivenessProvider.CAF, matchLivenessId)
            val faceAuthResponse = FaceAuthenticationAttemptResponse(
                requestId = "request123",
                data = FaceAttemptDetails(
                    id = "attempt456",
                    createdAt = "2023-01-01T00:00:00Z",
                    personId = accountId.value,
                    sourceIp = "**************",
                    registeredFaceImageUrl = "https://example.com/registered.jpg",
                    capturedFaceImageUrl = "https://example.com/captured.jpg",
                    isMatch = true,
                    similarity = 0.95,
                ),
            )

            every { cafAdapter.getFaceAuthenticationAttempt(matchLivenessId) } returns faceAuthResponse.right()

            val result = cafService.verifyMatch(livenessId)

            val expectedResult = LivenessMatchVerify(
                livenessId = livenessId,
                accountId = accountId,
                match = true,
                attempt = 1,
            )

            result shouldBe expectedResult.right()
            verify { cafAdapter.getFaceAuthenticationAttempt(matchLivenessId) }
        }

        @Test
        fun `should verify match as false when face authentication fails`() {
            val accountId = AccountId("account123")
            val matchLivenessId = "attempt456"
            val livenessId = LivenessId("liveness123", LivenessProvider.CAF, matchLivenessId)
            val faceAuthResponse = FaceAuthenticationAttemptResponse(
                requestId = "request123",
                data = FaceAttemptDetails(
                    id = "attempt456",
                    createdAt = "2023-01-01T00:00:00Z",
                    personId = accountId.value,
                    sourceIp = "**************",
                    registeredFaceImageUrl = "https://example.com/registered.jpg",
                    capturedFaceImageUrl = "https://example.com/captured.jpg",
                    isMatch = false,
                    similarity = 0.65,
                ),
            )

            every { cafAdapter.getFaceAuthenticationAttempt(matchLivenessId) } returns faceAuthResponse.right()

            val result = cafService.verifyMatch(livenessId)

            val expectedResult = LivenessMatchVerify(
                livenessId = livenessId,
                accountId = accountId,
                match = false,
                attempt = 1,
            )

            result shouldBe expectedResult.right()
            verify { cafAdapter.getFaceAuthenticationAttempt(matchLivenessId) }
        }

        @Test
        fun `should handle null result in face authentication response`() {
            val accountId = AccountId("account123")
            val matchLivenessId = "attempt456"
            val livenessId = LivenessId("liveness123", LivenessProvider.CAF, matchLivenessId)
            val faceAuthResponse = FaceAuthenticationAttemptResponse(
                requestId = "request123",
                data = FaceAttemptDetails(
                    id = "attempt456",
                    createdAt = "2023-01-01T00:00:00Z",
                    personId = accountId.value,
                    sourceIp = "**************",
                    registeredFaceImageUrl = "https://example.com/registered.jpg",
                    capturedFaceImageUrl = "https://example.com/captured.jpg",
                    isMatch = false,
                    similarity = 0.0,
                ),
            )

            every { cafAdapter.getFaceAuthenticationAttempt(matchLivenessId) } returns faceAuthResponse.right()

            val result = cafService.verifyMatch(livenessId)

            val expectedResult = LivenessMatchVerify(
                livenessId = livenessId,
                accountId = accountId,
                match = false,
                attempt = 1,
            )

            result shouldBe expectedResult.right()
            verify { cafAdapter.getFaceAuthenticationAttempt(matchLivenessId) }
        }

        @Test
        fun `should return error when adapter fails to get face authentication attempt`() {
            val matchLivenessId = "attempt456"
            val livenessId = LivenessId("liveness123", LivenessProvider.CAF, matchLivenessId)
            val exception = RuntimeException("CAF API error")

            every { cafAdapter.getFaceAuthenticationAttempt(matchLivenessId) } returns exception.left()

            val result = cafService.verifyMatch(livenessId)

            result shouldBe LivenessErrors.Error(exception).left()
            verify { cafAdapter.getFaceAuthenticationAttempt(matchLivenessId) }
        }
    }

    @DisplayName("CreateMatch")
    @Nested
    inner class CreateMatch {

        @Test
        fun `should create match successfully with valid JWT`() {
            val accountId = AccountId("account123")
            val referenceToken = "reference123"
            val livenessId = LivenessId("liveness123", LivenessProvider.CAF)
            val attemptId = "01K4BADFJVWSMCXJQHARAK26D0"
            val accountRegister = basicAccountRegisterData.copy(livenessId = livenessId)
            val jwt =
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOiIwMUs0QkFEN1JKVzdYM0JFSkM5VDZIM0hEMSIsInBlcnNvbklkIjoiQUNDT1VOVC1lY2FmZTNkYy1lYWNhLTQyYjItOGE1MC1mNjVkMDBiYjVlYTEiLCJpc0FsaXZlIjp0cnVlLCJpbWFnZVVybCI6Imh0dHBzOi8vbW9iaWxlLXByb2QtbGl2ZW5lc3MtYXR0ZW1wdHMuczMudXMtZWFzdC0xLmFtYXpvbmF3cy5jb20vZTE2ZmNhMWYtN2E2OS00ZjM1LWJiYTYtN2ZhMTJjYzExNDIwL25vdF9pZGVudGlmaWVkL2xpdmVuZXNzL2F0dGVtcHRfMTc1NzAyMTY0OTcyMy5wbmc_************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Fx4m5x2l9_4xOD4ZGFoeO6eExLYtCmUHlMp1zfPAMWY"

            every { accountRegisterRepository.findByAccountId(accountId) } returns accountRegister
            every { accountRegisterRepository.save(any()) } returns accountRegister

            val result = cafService.createMatch(jwt, accountId, referenceToken)

            val expectedLivenessId = livenessId.copy(matchLivenessId = attemptId)
            result shouldBe expectedLivenessId.right()
            verify { accountRegisterRepository.findByAccountId(accountId) }
            verify { accountRegisterRepository.save(any()) }
        }

        @Test
        fun `should return error when JWT has no faceAuthenticationAttemptId`() {
            val accountId = AccountId("account123")
            val referenceToken = "reference123"
            val jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************.YourValidSignatureHere"

            val result = cafService.createMatch(jwt, accountId, referenceToken)

            result.isLeft() shouldBe true
            result.leftOrNull().shouldBeInstanceOf<IllegalStateException>()
        }

        @Test
        fun `should return error when account has no CAF livenessId`() {
            val accountId = AccountId("account123")
            val referenceToken = "reference123"
            val livenessId = LivenessId("liveness123", LivenessProvider.FACETEC)
            val accountRegister = basicAccountRegisterData.copy(livenessId = livenessId)
            val jwt =
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOiIwMUs0QkFEN1JKVzdYM0JFSkM5VDZIM0hEMSIsInBlcnNvbklkIjoiQUNDT1VOVC1lY2FmZTNkYy1lYWNhLTQyYjItOGE1MC1mNjVkMDBiYjVlYTEiLCJpc0FsaXZlIjp0cnVlLCJpbWFnZVVybCI6Imh0dHBzOi8vbW9iaWxlLXByb2QtbGl2ZW5lc3MtYXR0ZW1wdHMuczMudXMtZWFzdC0xLmFtYXpvbmF3cy5jb20vZTE2ZmNhMWYtN2E2OS00ZjM1LWJiYTYtN2ZhMTJjYzExNDIwL25vdF9pZGVudGlmaWVkL2xpdmVuZXNzL2F0dGVtcHRfMTc1NzAyMTY0OTcyMy5wbmc_************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Fx4m5x2l9_4xOD4ZGFoeO6eExLYtCmUHlMp1zfPAMWY"

            every { accountRegisterRepository.findByAccountId(accountId) } returns accountRegister

            val result = cafService.createMatch(jwt, accountId, referenceToken)

            result.isLeft() shouldBe true
            result.leftOrNull().shouldBeInstanceOf<IllegalStateException>()
            verify { accountRegisterRepository.findByAccountId(accountId) }
        }

        @Test
        fun `should return error when account has no livenessId`() {
            val accountId = AccountId("account123")
            val referenceToken = "reference123"
            val accountRegister = basicAccountRegisterData.copy(livenessId = null)
            val jwt =
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOiIwMUs0QkFEN1JKVzdYM0JFSkM5VDZIM0hEMSIsInBlcnNvbklkIjoiQUNDT1VOVC1lY2FmZTNkYy1lYWNhLTQyYjItOGE1MC1mNjVkMDBiYjVlYTEiLCJpc0FsaXZlIjp0cnVlLCJpbWFnZVVybCI6Imh0dHBzOi8vbW9iaWxlLXByb2QtbGl2ZW5lc3MtYXR0ZW1wdHMuczMudXMtZWFzdC0xLmFtYXpvbmF3cy5jb20vZTE2ZmNhMWYtN2E2OS00ZjM1LWJiYTYtN2ZhMTJjYzExNDIwL25vdF9pZGVudGlmaWVkL2xpdmVuZXNzL2F0dGVtcHRfMTc1NzAyMTY0OTcyMy5wbmc_************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Fx4m5x2l9_4xOD4ZGFoeO6eExLYtCmUHlMp1zfPAMWY"

            every { accountRegisterRepository.findByAccountId(accountId) } returns accountRegister

            val result = cafService.createMatch(jwt, accountId, referenceToken)

            result.isLeft() shouldBe true
            result.leftOrNull().shouldBeInstanceOf<IllegalStateException>()
            verify { accountRegisterRepository.findByAccountId(accountId) }
        }

        @Test
        fun `should handle JWT decoding exception`() {
            val accountId = AccountId("account123")
            val referenceToken = "reference123"
            val invalidJwt = "invalid.jwt.token"

            val result = cafService.createMatch(invalidJwt, accountId, referenceToken)

            result.isLeft() shouldBe true
            result.leftOrNull().shouldBeInstanceOf<IllegalStateException>()
        }
    }

    val parsedDocumentResult = """{ 
    "requestId": "26d82f4c-4af3-428f-b63f-a3ef381acf62",
    "status": "APPROVED",
    "type": "rg",
    "images": {
        "front": "https://caf-prod-customers-storage.s3.us-east-1.amazonaws.com/2b56e2f5-bff2-4342-9d91-7930062e29be/transactions/68b04f1bfcbae60002a58e37_front.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=ASIAS6F2XPA767PMWDGQ%2F20250828%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250828T130026Z&X-Amz-Expires=3600&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEE0aCXVzLWVhc3QtMSJIMEYCIQCpm3lJr34VxbL0B48aRWup8OBRJeNHm3%2Fr3Uaksy2RZQIhAISzuZ2f2x3smBL0RpbIBK71DnuVVi9to3UbbBPSoHmOKpEDCKX%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEQBBoMMjAyMjU1MDcxMjk1IgxB%2BwFGbwcyA3ePw0oq5QIKdzKy2oM8tphZIYTkwj4S4sUKVJ1kFfqVEVdVhQQHcIW3VJTe2xaZfMJBOA62D6cIbJODN%2BgiN53M3a02m71mUrhddI%2Bv3tvOZgho2pYi2cw8Xv9d6TP4RT1tWCr3Xy%2FiEZsszDWCdBMKSpQ7Yi0Q8XaVOe0boM0R%2FUG34h%2BluClvXA3VQwoHRmeeuczS1C4OCN%2BYQ5yHnof3kK467vIkzynUJ0ES297iN46RtUF6toTv6MrT6daxDwL5%2FY87nV3DXjBdd2hWrOYuZQWZViG4hYXKOiuHV9DespvIxyLhY5DMxqLjXmNV9K4L%2FBrvn0HelJYXjqggCaUOUQZHCgwZdYTiLCvT8rdLkZWlrKdP4l4OY%2F54X52ZNsPHkaryzGc0yUBNa4ku6fDyhuebuTBond2JpFroeSo4l9waS7iSRMvlvFRqpShVAD9XuBSpHK9KI3Pw7MPmqfTUpyqr7D2j%2BLMyoH0wmI7BxQY6nQFtAZEaiq6zS53NlaR%2BQk0r%2BD1M0kWKIgkJeQW7inLU9RzCPCCeeg0SnP7bbHGApjkva23NW3q2PKYbmeIhl8mDsPWFBKoAo8m%2Bvz31%2FNGNIcZHwD5Xd6rUbxiqAFm0eZkakisXinV8TxGFR3xmf80NVxiGpSc%2FmhKdrpd8U1a8Px24tcYP185AIHXrQCfHcazyrn%2BIbSRXDMyzLGX5&X-Amz-Signature=e9029713ec06303c9ae1be2534dcb357a94a5fccad8e5853b520a30a5fb8d08c&X-Amz-SignedHeaders=host&x-amz-checksum-mode=ENABLED&x-id=GetObject",
        "back": "https://caf-prod-customers-storage.s3.us-east-1.amazonaws.com/2b56e2f5-bff2-4342-9d91-7930062e29be/transactions/68b04f1bfcbae60002a58e37_back.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=ASIAS6F2XPA767PMWDGQ%2F20250828%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250828T130026Z&X-Amz-Expires=3600&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEE0aCXVzLWVhc3QtMSJIMEYCIQCpm3lJr34VxbL0B48aRWup8OBRJeNHm3%2Fr3Uaksy2RZQIhAISzuZ2f2x3smBL0RpbIBK71DnuVVi9to3UbbBPSoHmOKpEDCKX%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEQBBoMMjAyMjU1MDcxMjk1IgxB%2BwFGbwcyA3ePw0oq5QIKdzKy2oM8tphZIYTkwj4S4sUKVJ1kFfqVEVdVhQQHcIW3VJTe2xaZfMJBOA62D6cIbJODN%2BgiN53M3a02m71mUrhddI%2Bv3tvOZgho2pYi2cw8Xv9d6TP4RT1tWCr3Xy%2FiEZsszDWCdBMKSpQ7Yi0Q8XaVOe0boM0R%2FUG34h%2BluClvXA3VQwoHRmeeuczS1C4OCN%2BYQ5yHnof3kK467vIkzynUJ0ES297iN46RtUF6toTv6MrT6daxDwL5%2FY87nV3DXjBdd2hWrOYuZQWZViG4hYXKOiuHV9DespvIxyLhY5DMxqLjXmNV9K4L%2FBrvn0HelJYXjqggCaUOUQZHCgwZdYTiLCvT8rdLkZWlrKdP4l4OY%2F54X52ZNsPHkaryzGc0yUBNa4ku6fDyhuebuTBond2JpFroeSo4l9waS7iSRMvlvFRqpShVAD9XuBSpHK9KI3Pw7MPmqfTUpyqr7D2j%2BLMyoH0wmI7BxQY6nQFtAZEaiq6zS53NlaR%2BQk0r%2BD1M0kWKIgkJeQW7inLU9RzCPCCeeg0SnP7bbHGApjkva23NW3q2PKYbmeIhl8mDsPWFBKoAo8m%2Bvz31%2FNGNIcZHwD5Xd6rUbxiqAFm0eZkakisXinV8TxGFR3xmf80NVxiGpSc%2FmhKdrpd8U1a8Px24tcYP185AIHXrQCfHcazyrn%2BIbSRXDMyzLGX5&X-Amz-Signature=e8e87fbb09768a5f8528d0247555fb6dc62e7df57753f24f0e771e62b1314216&X-Amz-SignedHeaders=host&x-amz-checksum-mode=ENABLED&x-id=GetObject"
    },
    "sections": {
        "ocr": {
            "headerRgFront": "REPUBLICA FEDERATIVA DO BRASIL",
            "federativeUnit": "RIO GRANDE DO SUL",
            "issuingAuthorityHeader": "SECRETARIA DA SEGURANCA PUBLICA",
            "institute": "INSTITUTO-GERAL DE PERICIAS",
            "department": "DEPARTAMENTO DE IDENTIFICACAO",
            "documentTypeRgFront": "CARTEIRA DE IDENTIDADE",
            "rg": "1104417397",
            "rgMask": "9999999999",
            "via": "2 VIA.",
            "issueDate": "08/01/2016",
            "name": "CAIO PEREIRA DA SILVA",
            "fatherName": "MANOEL CASTRO DA SILVA",
            "parentsSeparator": "",
            "motherName": "MARIA LUCIA PEREIRA DA SILVA",
            "birthPlace": "PELOTAS RS",
            "birthDate": "09/05/1997",
            "referenceDocument": "C NASC 57815 PELOTAS RS 2A ZONA LV A276 FL 71",
            "cpf": "03761255004",
            "cpfMask": "999.999.999-99",
            "graphicName": "VALID",
            "footerRgBack": "",
            "issuingAuthority": "SSP",
            "issueState": "RS",
            "lawRgBack": "LEI NO 7.116 DE 29/08/83",
            "rgIssuingAuthority": "SSP",
            "rgIssueState": "RS"
        },
        "documentLiveness": {
            "statusCode": "01",
            "data": {
                "front": {
                    "decision": true,
                    "confidence": "HIGH",
                    "indicators": [
                        {
                            "message": "The document is probably real.",
                            "id": "real_doc"
                        },
                        {
                            "message": "In the document was not found editor trace.",
                            "id": "editor_not_found_in_doc_metadata"
                        }
                    ]
                },
                "back": {
                    "decision": true,
                    "confidence": "HIGH",
                    "indicators": [
                        {
                            "message": "The document is probably real.",
                            "id": "real_doc"
                        },
                        {
                            "message": "In the document was not found editor trace.",
                            "id": "editor_not_found_in_doc_metadata"
                        }
                    ]
                }
            }
        },
        "pfCpfData": {
            "statusCode": "01",
            "data": {
                "taxIdNumber": "03761255004",
                "name": "CAIO PEREIRA DA SILVA",
                "socialName": "",
                "taxIdStatus": "REGULAR",
                "birthDate": "09/05/1997",
                "fallback": "false"
            }
        },
        "cpf": {
            "statusCode": "00",
            "registrationStatusCode": "00",
            "registrationStatusMessage": "REGULAR",
            "name": "CAIO PEREIRA DA SILVA",
            "socialName": "",
            "birthDate": "09/05/1997",
            "issueDate": "01/11/2011",
            "deathYear": "",
            "deathMessage": ""
        },
        "documentscopy": {
            "status": "APPROVED",
            "fraud": false,
            "evaluation": [],
            "reviewDate": "2025-08-28T12:44:48.014Z"
        }
    },
    "history": [
        {
            "type": "START_PROCESS",
            "status": "PROCESSING",
            "date": "2025-08-28T12:44:11.754Z"
        },
        {
            "status": "APPROVED",
            "type": "STATUS_UPDATE",
            "date": "2025-08-28T12:44:48.095Z"
        }
    ],
    "metadata": {
        "tenantId": "2b56e2f5-bff2-4342-9d91-7930062e29be",
        "origin": "API",
        "templateOrigin": "TRUST"
    },
    "templateId": "689a5b1ab14df50002012dc3",
    "createdAt": "2025-08-28T12:44:11.113Z",
    "detectedType": {
        "execution": "rg"
    },
    "fraud": false,
    "documentscopyRequestDate": "2025-08-28T12:44:44.050Z",
    "fraudScore": {
        "total": 500
    },
    "id": "68b04f1bfcbae60002a58e37",
    "statusReasons": [
        {
            "category": "VALIDATION",
            "code": "has_document",
            "status": "VALID",
            "resultStatus": "APPROVED"
        }
    ],
    "attributes": {},
    "files": []
}"""
}