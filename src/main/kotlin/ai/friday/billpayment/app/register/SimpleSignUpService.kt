package ai.friday.billpayment.app.register

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.AccountRegisterDocument
import ai.friday.billpayment.app.account.AccountRegisterService
import ai.friday.billpayment.app.account.AgreementData
import ai.friday.billpayment.app.account.BasicRegisterService
import ai.friday.billpayment.app.account.DocumentInfo
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.account.ExternalRegisterStatus
import ai.friday.billpayment.app.account.IdentityValidationStatus
import ai.friday.billpayment.app.account.MonthlyIncome
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.SendSimpleSignUpDocumentsRequest
import ai.friday.billpayment.app.account.SetupAccountResult
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.account.UserFilesConfiguration
import ai.friday.billpayment.app.account.getCPF
import ai.friday.billpayment.app.account.getName
import ai.friday.billpayment.app.caf.CafService
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.ECMProvider
import ai.friday.billpayment.app.integrations.ECMProviderException
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.liveness.LivenessProvider
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.billpayment.app.register.kyc.KycDossier
import ai.friday.billpayment.app.register.kyc.KycService
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Property
import java.text.Normalizer
import java.time.LocalDate
import java.time.Period
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@FridayMePoupe
open class SimpleSignUpService(
    private val accountRepository: AccountRepository,
    private val accountRegisterRepository: AccountRegisterRepository,
    private val livenessService: LivenessService,
    private val cafService: CafService,
    private val kycService: KycService,
    private val ecmProvider: ECMProvider,
    private val userFilesConfiguration: UserFilesConfiguration,
    private val objectRepository: ObjectRepository,
    private val messagePublisher: MessagePublisher,
    private val registerService: BasicRegisterService,
    private val accountRegisterService: AccountRegisterService,
    @Property(name = "features.inAppSubscription", defaultValue = "0") private val inAppSubscription: Float,
) {

    @field:Property(name = "sqs.simpleSignUpQueueName")
    lateinit var simpleSignUpQueueName: String

    open fun validate(request: UserDataValidationRequest): UserDataValidationResult {
        val markers = append("request", request)

        val account = accountRepository.findAccountByDocumentOrNull(request.document.value)
        markers.andAppend("account", account)

        return if (account != null) {
            if (account.mobilePhone == request.mobilePhone.msisdn) {
                UserDataValidationResult(valid = true, accountId = account.accountId)
            } else {
                UserDataValidationResult(valid = false, accountId = null)
            }
        } else {
            val accountRegister =
                accountRegisterRepository.findByVerifiedMobilePhone(request.mobilePhone).firstOrNull()
            markers.andAppend("accountRegister", accountRegister)

            if (accountRegister == null) {
                UserDataValidationResult(valid = true, accountId = null)
            } else {
                if (accountRegister.documentInfo?.cpf == request.document.value && accountRegister.externalId?.providerName == AccountProviderName.MODATTA) {
                    UserDataValidationResult(valid = true, accountId = accountRegister.accountId)
                } else {
                    UserDataValidationResult(valid = false, accountId = null)
                }
            }
        }.also {
            markers.andAppend("result", it)

            logger.info(markers, "SimpleSignUpService#validate")
        }
    }

    // Aqui só entra account Modatta
    open fun signUp(simpleSignUpRequest: SimpleSignUpRequest): Either<Exception, SimpleSignUpResult> {
        val markers = Markers.append("request", simpleSignUpRequest)

        val validationResult = validate(
            UserDataValidationRequest(
                document = simpleSignUpRequest.document,
                mobilePhone = simpleSignUpRequest.mobilePhone,
            ),
        )
        markers.andAppend("validationResult", validationResult)

        if (!validationResult.valid) {
            logger.warn(markers, "SimpleSignUpService#signUp")
            return IllegalStateException().left()
        }

        val accountRegister = if (validationResult.accountId != null) {
            findIncompleteAccountRegisterData(validationResult.accountId, simpleSignUpRequest.externalId).getOrElse {
                logger.warn(markers, "SimpleSignUpService#signUp")
                return it.left()
            }
        } else {
            simpleSignUpRequest.createAccountRegisterData()
        }

        val event = SimpleSignUpPendingMessage(
            livenessId = simpleSignUpRequest.livenessId.value,
            userContractKey = simpleSignUpRequest.userContractKey,
            userContractSignature = simpleSignUpRequest.userContractSignature,
            accountId = accountRegister.accountId.value,
            selfieCaptured = false,
            userContractCaptured = false,
            deduplicationVerified = false,
            identityVerified = false,
            kycDossierGenerated = false,
            hasPassedRiskAnalysis = false,
            ecmDocumentsSent = false,
            accountActivated = false,
        )
        messagePublisher.sendMessage(simpleSignUpQueueName, event)
        return SimpleSignUpResult(accountRegister.accountId).right()
    }

    // Aqui entra account Modatta e Friday simplificado
    fun continueSignUp(event: SimpleSignUpPendingMessage): Either<Exception, SimpleSignUpPendingMessage> {
        val selfieStep = fun(
            accountRegister: AccountRegisterData,
            event: SimpleSignUpPendingMessage,
        ): Either<Exception, Pair<AccountRegisterData, SimpleSignUpPendingMessage>> {
            return try {
                if (!event.selfieCaptured) {
                    val livenessId = accountRegister.livenessId ?: LivenessId(event.livenessId, LivenessProvider.FACETEC)
                    accountRegister.captureSelfie(livenessId).map {
                        Pair(it, event.copy(selfieCaptured = true))
                    }
                } else {
                    Pair(accountRegister, event).right()
                }
            } catch (e: Exception) {
                e.left()
            }
        }

        val userContractStep = fun(
            accountRegister: AccountRegisterData,
            event: SimpleSignUpPendingMessage,
        ): Either<Exception, Pair<AccountRegisterData, SimpleSignUpPendingMessage>> {
            return try {
                if (!event.userContractCaptured) {
                    accountRegister.captureUserContract(
                        userContractKey = event.userContractKey,
                        userContractSignature = event.userContractSignature,
                    ).map {
                        Pair(it, event.copy(userContractCaptured = true))
                    }
                } else {
                    Pair(accountRegister, event).right()
                }
            } catch (e: Exception) {
                e.left()
            }
        }

        val documentVerificationStep = fun(
            accountRegister: AccountRegisterData,
            event: SimpleSignUpPendingMessage,
        ): Either<Exception, Pair<AccountRegisterData, SimpleSignUpPendingMessage>> {
            if (accountRegister.externalId?.providerName == AccountProviderName.MODATTA || event.identityVerified) {
                return Pair(accountRegister, event).right()
            }

            if (accountRegister.identityValidationStatus != null) {
                return Pair(accountRegister, event.copy(identityVerified = true)).right()
            }

            val result = registerService.validateIdentity(accountRegister.accountId)

            val accountRegisterData = result.getOrElse {
                return IllegalStateException("Failed to retreive validated identity. $it").left()
            }

            return Pair(accountRegisterData, event.copy(identityVerified = true)).right()
        }

        val deduplicationStep = fun(
            accountRegister: AccountRegisterData,
            event: SimpleSignUpPendingMessage,
        ): Either<Exception, Pair<AccountRegisterData, SimpleSignUpPendingMessage>> {
            return try {
                if (!event.deduplicationVerified) {
                    val result = accountRegister.deduplicate().getOrElse { return it.left() }

                    Pair(result, event.copy(deduplicationVerified = true)).right()
                } else {
                    Pair(accountRegister, event).right()
                }
            } catch (e: Exception) {
                e.left()
            }
        }

        val kycDossierStep = fun(
            accountRegister: AccountRegisterData,
            event: SimpleSignUpPendingMessage,
        ): Either<Exception, Pair<AccountRegisterData, SimpleSignUpPendingMessage>> {
            return try {
                if (!event.kycDossierGenerated) {
                    accountRegister.generateKyc().map { (kycDossier, accountRegister) ->
                        Pair(accountRegister, event.copy(kycDossierGenerated = true, kycDossier = kycDossier))
                    }
                } else {
                    Pair(accountRegister, event).right()
                }
            } catch (e: Exception) {
                e.left()
            }
        }

        val riskAnalysisStep = fun(
            accountRegister: AccountRegisterData,
            event: SimpleSignUpPendingMessage,
        ): Either<Exception, Pair<AccountRegisterData, SimpleSignUpPendingMessage>> {
            return try {
                if (!event.hasPassedRiskAnalysis && event.kycDossier != null) {
                    riskAnalysis(event.kycDossier, AccountId(event.accountId)).map {
                        Pair(accountRegister, event.copy(hasPassedRiskAnalysis = true))
                    }
                } else {
                    Pair(accountRegister, event).right()
                }
            } catch (e: Exception) {
                e.left()
            }
        }

        val ecmDocumentsStep = fun(
            accountRegister: AccountRegisterData,
            event: SimpleSignUpPendingMessage,
        ): Either<Exception, Pair<AccountRegisterData, SimpleSignUpPendingMessage>> {
            return try {
                if (!event.ecmDocumentsSent) {
                    accountRegister.sendECMDocuments().map {
                        Pair(it, event.copy(ecmDocumentsSent = true))
                    }
                } else {
                    Pair(accountRegister, event).right()
                }
            } catch (e: Exception) {
                e.left()
            }
        }

        val activateAccountStep = fun(
            accountRegister: AccountRegisterData,
            event: SimpleSignUpPendingMessage,
        ): Either<Exception, Pair<AccountRegisterData, SimpleSignUpPendingMessage>> {
            return try {
                if (!event.accountActivated) {
                    accountRegister.activateAccount().map {
                        Pair(it, event.copy(accountActivated = true))
                    }
                } else {
                    Pair(accountRegister, event).right()
                }
            } catch (e: Exception) {
                e.left()
            }
        }

        val stepList =
            listOf(
                selfieStep,
                userContractStep,
                deduplicationStep,
                documentVerificationStep,
                kycDossierStep,
                riskAnalysisStep,
                ecmDocumentsStep,
                activateAccountStep,
            )

        var updatedEvent = event
        var accountRegister = accountRegisterRepository.findByAccountId(AccountId(event.accountId))

        stepList.forEach { currentStep ->
            val result = currentStep(accountRegister, updatedEvent).getOrElse {
                if (it is RiskAnalysisException) {
                    accountRegister.processRiskAnalysisFailed(it.reasons)
                    return updatedEvent.right()
                }

                logger.error(append("updatedEvent", updatedEvent), "SimpleSignUpService#signUp", it)

                if (updatedEvent == event) {
                    return it.left()
                } else {
                    messagePublisher.sendMessage(simpleSignUpQueueName, updatedEvent)
                    return updatedEvent.right()
                }
            }

            logger.info(append("updatedEvent", updatedEvent), "SimpleSignUpService#signUp")

            accountRegister = result.first
            updatedEvent = result.second
        }

        return updatedEvent.right()
    }

    private fun AccountRegisterData.processRiskAnalysisFailed(riskAnalysisFailedReasons: List<RiskAnalysisFailedReason>) {
        val shouldReopen = riskAnalysisFailedReasons.all { it.action == RiskAnalysisFailedAction.REOPEN }
        val shouldReject = riskAnalysisFailedReasons.any { it.action == RiskAnalysisFailedAction.REJECT }

        if (shouldReopen) {
            registerService.reopenRegister(this, riskAnalysisFailedReasons)
        } else if (shouldReject) {
            registerService.rejectRegister(this, riskAnalysisFailedReasons)
        } else {
            registerService.upgradeRegister(this, riskAnalysisFailedReasons, shouldNotify = true)
        }
    }

    private fun riskAnalysis(
        kyc: KycDossier,
        accountId: AccountId,
    ): Either<RiskAnalysisException, Unit> {
        val markers = append("accountId", accountId.value)
        val deniedReasons = mutableListOf<RiskAnalysisFailedReason>()
        val accountRegister = accountRegisterRepository.findByAccountId(accountId, true)

        kyc.verifyRisks(accountRegister).mapLeft {
            deniedReasons.addAll(it)
        }

        markers.andAppend("deniedReasons", deniedReasons)

        logger.info(markers, "SimpleSignUpService#riskAnalysis")

        if (deniedReasons.isEmpty()) {
            accountRegisterRepository.save(accountRegister.copy(riskAnalysisFailedReasons = null))
            return Unit.right()
        }

        return RiskAnalysisException(reasons = deniedReasons).left()
    }

    private fun getAge(birthdate: LocalDate): Int {
        return Period.between(
            birthdate,
            getZonedDateTime().toLocalDate(),
        ).years
    }

    private fun SimpleSignUpRequest.createAccountRegisterData(): AccountRegisterData {
        val partialAccount = accountRepository.create(
            username = name,
            emailAddress = email,
            registrationType = RegistrationType.BASIC,
            subscriptionType = SubscriptionType.random(inAppSubscription),
        )

        return accountRegisterRepository.create(
            accountId = partialAccount.id,
            emailAddress = email,
            username = name,
            mobilePhone = mobilePhone,
            mobilePhoneVerified = true,
            livenessId = livenessId,
            externalId = externalId,
            documentInfo = DocumentInfo(
                name = name,
                cpf = document.value,
                birthDate = birthDate,
                fatherName = "",
                motherName = "",
                rg = "",
                docType = document.type,
                cnhNumber = null,
                orgEmission = document.issuer,
                expeditionDate = null,
                birthCity = "",
                birthState = document.issuerRegion,
            ),
            monthlyIncome = MonthlyIncome(0, 2_000_00),
            registrationType = RegistrationType.BASIC,
        )
    }

    private fun findIncompleteAccountRegisterData(
        accountId: AccountId,
        externalId: ExternalId,
    ): Either<Exception, AccountRegisterData> {
        return try {
            val accountRegister = accountRegisterRepository.findByAccountId(accountId)
            if (accountRegister.externalId == externalId) {
                accountRegister.right()
            } else {
                IllegalStateException().left()
            }
        } catch (e: ItemNotFoundException) {
            IllegalStateException().left()
        }
    }

    private fun AccountRegisterData.captureSelfie(livenessId: LivenessId): Either<Exception, AccountRegisterData> {
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("livenessId", livenessId.value)

        if (uploadedSelfie != null) {
            logger.info(markers, "AccountRegisterData#captureSelfie")
            return this.right()
        }

        val storedObject = accountRegisterService.processSelfie(accountId, livenessId).getOrElse {
            logger.error(markers, "AccountRegisterData#captureSelfie", it)
            return it.left()
        }
        markers.andAppend("storedObject", storedObject)

        logger.info(markers, "AccountRegisterData#captureSelfie")
        return this.copy(
            uploadedSelfie = storedObject,
        ).right().also {
            it.map { updatedAccountRegisterData ->
                accountRegisterRepository.save(updatedAccountRegisterData)
            }
        }
    }

    private fun AccountRegisterData.captureUserContract(
        userContractKey: String,
        userContractSignature: String,
    ): Either<Exception, AccountRegisterData> {
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("userContractKey", userContractKey)
            .andAppend("userContractSignature", userContractSignature)

        try {
            val acceptedAt = getZonedDateTime()
            val userContractFile = StoredObject(
                region = userFilesConfiguration.region,
                bucket = userFilesConfiguration.bucket,
                key = "${userFilesConfiguration.path}/${accountId.value}/${userFilesConfiguration.contractPrefix}${acceptedAt.toEpochSecond()}.pdf",
            )
            markers.andAppend("storedObject", userContractFile)

            val fromBucket = if (this.externalId?.providerName == AccountProviderName.MODATTA) {
                userFilesConfiguration.modattaUserDocumentsBucket
            } else {
                userFilesConfiguration.bucket
            }

            objectRepository.copyObject(
                fromBucket = fromBucket,
                fromKey = userContractKey,
                toBucket = userContractFile.bucket,
                toKey = userContractFile.key,
            )

            logger.info(markers, "AccountRegisterData#captureUserContract")
            return this.copy(
                agreementData = AgreementData(
                    acceptedAt = acceptedAt,
                    userContractFile = userContractFile,
                    userContractSignature = userContractSignature,
                    declarationOfResidencyFile = null,
                ),
            ).right().also {
                it.map { updatedAccountRegisterData ->
                    accountRegisterRepository.save(updatedAccountRegisterData)
                }
            }
        } catch (e: Exception) {
            logger.error(markers, "AccountRegisterData#captureUserContract", e)
            return e.left()
        }
    }

    private fun AccountRegisterData.deduplicate(): Either<Exception, AccountRegisterData> {
        val livenessExternalId = externalId?.let { AccountId(it.value) } ?: accountId
        val logName = "SimpleSignUpService#riskAnalysis"
        val markers = append("livenessExternalId", livenessExternalId.value)

        if (livenessEnrollmentVerification.isVerified) {
            logger.info(markers, logName)
            return this.right()
        }

        val result = if (livenessId?.provider == LivenessProvider.CAF) {
            cafService.verifyDuplication(livenessExternalId)
                .getOrElse {
                    logger.error(markers.andAppend("livenessError", it), logName)
                    return IllegalStateException("Unable to verify liveness").left()
                }
        } else {
            livenessService.verifyDuplication(livenessExternalId)
                .getOrElse {
                    logger.error(markers.andAppend("livenessError", it), logName)
                    return IllegalStateException("Unable to verify liveness").left()
                }
        }

        if (!result.isVerified) {
            logger.error(markers.andAppend("livenessEnrollmentVerification", result), logName)
            return IllegalStateException("Liveness enrollment not verified yet.").left()
        }

        return accountRegisterRepository.save(copy(livenessEnrollmentVerification = result)).right()
            .also {
                logger.info(
                    markers.andAppend("livenessEnrollmentVerification.hasDuplication", result.isVerifiedWithDuplication)
                        .andAppend(
                            "livenessEnrollmentVerification.hasFraudIndication",
                            result.isVerifiedWithFraudIndication,
                        ),
                    logName,
                )
            }
    }

    private fun AccountRegisterData.generateKyc(): Either<Exception, Pair<KycDossier, AccountRegisterData>> {
        val markers = append("accountId", accountId.value)

        val currentPep = politicallyExposed
            ?: return IllegalStateException("PEP must be at least self declared when generating KYC").left()

        val (storedObject, kycDossier) = kycService.generate(this).getOrElse {
            logger.error(markers, "AccountRegisterData#generateKyc", it)
            return it.left()
        }

        logger.info(markers.andAppend("storedObject", storedObject), "AccountRegisterData#generateKyc")

        return Pair(
            kycDossier,
            this.copy(
                politicallyExposed = currentPep.copy(query = kycDossier.pep),
                kycFile = storedObject,
            ),
        ).right().also {
            it.map { (_, updatedAccountRegisterData) ->
                accountRegisterRepository.save(updatedAccountRegisterData)
            }
        }
    }

    private fun AccountRegisterData.sendECMDocuments(): Either<Exception, AccountRegisterData> {
        val markers = Markers.append("accountId", accountId.value)

        val sendAccountRegisterDocumentsResponse = ecmProvider.sendSimpleSignUpDocuments(
            SendSimpleSignUpDocumentsRequest(
                id = accountId.value,
                name = getName(),
                document = getCPF()!!,
                emailAddress = emailAddress,
            ).apply {
                this.userSelfie = uploadedSelfie!!.toAccountRegisterDocument("Selfie - ${accountId.value}")
                this.userKYC = kycFile!!.toAccountRegisterDocument("KYC - ${accountId.value}")
                this.userContract = agreementData!!.userContractFile.toAccountRegisterDocument("Contrato - ${accountId.value}")
            },
        )
        markers.andAppend("sendAccountRegisterDocumentsResponse", sendAccountRegisterDocumentsResponse)

        if (!sendAccountRegisterDocumentsResponse.success) {
            logger.error(markers, "AccountRegisterData#sendECMDocuments")
            return ECMProviderException(sendAccountRegisterDocumentsResponse.status).left()
        }

        logger.info(markers, "AccountRegisterData#sendECMDocuments")
        return this.right()
    }

    private fun AccountRegisterData.createDocumentscopyTransaction(): Either<Exception, AccountRegisterData> {
        val markers = append("accountId", accountId.value)
        val logName = "AccountRegisterData#createDocumentscopyTransaction"

        if (livenessId?.provider != LivenessProvider.CAF) {
            logger.info(markers.andAppend("provider", livenessId?.provider?.toString()).andAppend("context", "skipping for non-CAF provider"), logName)
            return this.right()
        }

        if (documentScan == null || documentScan.documentScanId.provider != LivenessProvider.CAF) {
            logger.warn(markers.andAppend("documentScanProvider", documentScan?.documentScanId?.provider?.toString()).andAppend("context", "no CAF document scan found"), logName)
            return this.right()
        }

        return cafService.createDocumentscopyTransaction(
            documentScanId = documentScan.documentScanId,
            document?.value,
            name = nickname,
            birthDate = birthDate,
        ).fold(
            ifLeft = { exception ->
                logger.error(markers.andAppend("documentScanId", documentScan.documentScanId.value), logName, exception)
                exception.left()
            },
            ifRight = { transactionId ->
                logger.info(
                    markers.andAppend("documentscopyTransactionId", transactionId)
                        .andAppend("documentScanId", documentScan.documentScanId.value).andAppend("context", "documentscopy transaction created successfully"),
                    logName,
                )

                // Salvar o documentscopyTransactionId no accountRegister
                val updatedAccountRegister = this.copy(documentScan = this.documentScan.copy(documentScanId = this.documentScan.documentScanId.copy(documentscopyTransactionId = transactionId)))
                accountRegisterRepository.save(updatedAccountRegister)

                updatedAccountRegister.right()
            },
        )
    }

    private fun AccountRegisterData.checkDocumentscopyStatus(): Either<Exception, AccountRegisterData> {
        val markers = append("accountId", accountId.value)
        val logName = "AccountRegisterData#checkDocumentscopyStatus"

        if (livenessId?.provider != LivenessProvider.CAF) {
            logger.info(markers.andAppend("provider", livenessId?.provider?.toString()).andAppend("context", "skipping for non-CAF provider"), logName)
            return this.right()
        }

        val actualDocumentscopyTransactionId = documentScan?.documentScanId?.documentscopyTransactionId ?: run {
            logger.warn(markers.andAppend("context", "no documentscopyTransactionId found"), logName)
            return IllegalStateException("No documentscopy transaction ID found").left()
        }

        try {
            val transactionResponse = cafService.getTransaction(actualDocumentscopyTransactionId).getOrElse { exception ->
                logger.error(markers.andAppend("documentscopyTransactionId", actualDocumentscopyTransactionId), logName, exception)
                return exception.left()
            }

            if (transactionResponse.status != "APPROVED") {
                logger.warn(markers.andAppend("status", transactionResponse.status).andAppend("documentscopyTransactionId", actualDocumentscopyTransactionId).andAppend("context", "documentscopy not yet completed"), logName)
                return IllegalStateException("Documentscopy not yet completed").left()
            }

            val documentscopySection = transactionResponse.sections?.documentscopy
            if (documentscopySection == null) {
                logger.warn(markers.andAppend("documentscopyTransactionId", actualDocumentscopyTransactionId).andAppend("context", "no documentscopy section found"), logName)
                return this.right()
            }

            logger.info(
                markers.andAppend("documentscopyTransactionId", actualDocumentscopyTransactionId)
                    .andAppend("documentscopyStatus", documentscopySection.status)
                    .andAppend("context", "documentscopy completed successfully")
                    .andAppend("documentscopyFraud", documentscopySection.fraud),
                logName,
            )

            return this.right()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return e.left()
        }
    }

    private fun AccountRegisterData.activateAccount(): Either<Exception, AccountRegisterData> {
        val markers = append("accountId", accountId.value)

        val result =
            registerService.updateAccountStatus(this.getCPF()!!, ExternalRegisterStatus.APPROVED).getOrElse {
                logger.error(markers.andAppend("error", it), "AccountRegisterData#activateAccount")
                return it.left()
            }

        if (result is SetupAccountResult.AccountApproved) {
            logger.info(markers.andAppend("result", result), "AccountRegisterData#activateAccount")
            return this.right()
        }

        logger.error(markers.andAppend("error", result), "AccountRegisterData#activateAccount")

        return Exception(result.toString()).left()
    }

    private fun StoredObject.toAccountRegisterDocument(name: String): AccountRegisterDocument {
        return accountRegisterRepository.getDocumentInputStream(this).use {
            AccountRegisterDocument(
                name = name,
                content = it.readAllBytes(),
                extension = this.getExtension(),
            )
        }
    }

    private fun StoredObject.getExtension() = key.substringAfterLast('.', "")

    private fun KycDossier.verifyRisks(accountRegisterData: AccountRegisterData): Either<List<RiskAnalysisFailedReason>, Unit> {
        val deniedReasons = mutableListOf<RiskAnalysisFailedReason>()
        val age = getAge(officialData.birthDate)

        val isUnderage = age < 18
        val isEnderlyPerson = age >= 80
        val hasNameMismatch =
            (
                accountRegisterData.documentInfo?.name?.sanitize()?.uppercase()
                    ?: accountRegisterData.nickname.sanitize()
                        .uppercase()
                ) != officialData.name.sanitize().uppercase()
        val hasBirthDateMismatch =
            accountRegisterData.birthDate != null && accountRegisterData.birthDate != officialData.birthDate
        val hasObitIndication = officialData.hasObitIndication
        val irregularDocument = !officialData.regular
        val hasSanctions = sanctions.isNotEmpty()
        val isMediaExposedPerson = mep.isExposed
        val isPoliticallyExposedPerson = accountRegisterData.politicallyExposed!!.isExposed
        val documentValidationRejected =
            accountRegisterData.identityValidationStatus == IdentityValidationStatus.REJECTED
        val unableToValidateDocument =
            accountRegisterData.identityValidationStatus == IdentityValidationStatus.UNKNOWN
        val fraudListMatch = accountRegisterData.fraudListMatch

        if (!accountRegisterData.livenessEnrollmentVerification.isVerified) {
            throw IllegalStateException("Liveness enrollment should have been verified at this point")
        }

        if (fraudListMatch) {
            deniedReasons.add(RiskAnalysisFailedReason.FRAUD_LIST_MATCH)
        }

        if (isUnderage) {
            deniedReasons.add(RiskAnalysisFailedReason.UNDERAGE)
        }

        if (isEnderlyPerson) {
            deniedReasons.add(RiskAnalysisFailedReason.ELDERLY_PERSON)
        }

        if (hasNameMismatch) {
            deniedReasons.add(RiskAnalysisFailedReason.NAME_MISMATCH)
        }

        if (hasBirthDateMismatch) {
            deniedReasons.add(RiskAnalysisFailedReason.BIRTHDATE_MISMATCH)
        }

        if (hasObitIndication) {
            deniedReasons.add(RiskAnalysisFailedReason.HAS_OBIT_INDICATION)
        }

        if (irregularDocument) {
            deniedReasons.add(RiskAnalysisFailedReason.IRREGULAR_DOCUMENT)
        }

        if (hasSanctions) {
            deniedReasons.add(RiskAnalysisFailedReason.HAS_SANCTIONS)
        }

        if (isMediaExposedPerson) {
            deniedReasons.add(RiskAnalysisFailedReason.MEDIA_EXPOSED_PERSON)
        }

        if (isPoliticallyExposedPerson) {
            deniedReasons.add(RiskAnalysisFailedReason.POLITICALLY_EXPOSED_PERSON)
        }

        if (accountRegisterData.livenessEnrollmentVerification.isVerifiedWithDuplication) {
            deniedReasons.add(RiskAnalysisFailedReason.FACE_MATCH_DUPLICATION)
        }

        if (accountRegisterData.livenessEnrollmentVerification.isVerifiedWithFraudIndication) {
            deniedReasons.add(RiskAnalysisFailedReason.FACE_MATCH_FRAUD_INDICATION)
        }

        if (documentValidationRejected) {
            deniedReasons.add(RiskAnalysisFailedReason.DOCUMENT_VALIDATION_REJECTED)
        }

        if (unableToValidateDocument) {
            deniedReasons.add(RiskAnalysisFailedReason.UNABLE_TO_VALIDATE_DOCUMENT)
        }

        if (deniedReasons.isEmpty()) {
            return Unit.right()
        }

        return deniedReasons.left()
    }

    private fun String.sanitize(): String {
        val normalizer = Normalizer.normalize(this, Normalizer.Form.NFD)

        val regex = "\\p{InCombiningDiacriticalMarks}+".toRegex()

        return regex.replace(normalizer, "")
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SimpleSignUpService::class.java)
    }
}

data class UserDataValidationRequest(
    val document: Document,
    val mobilePhone: MobilePhone,
)

data class UserDataValidationResult(
    val valid: Boolean,
    val accountId: AccountId?,
)

data class SimpleSignUpRequest(
    val externalId: ExternalId,
    val name: String,
    val document: Document,
    val birthDate: LocalDate,
    val email: EmailAddress,
    val mobilePhone: MobilePhone,
    val livenessId: LivenessId,
    val userContractKey: String,
    val userContractSignature: String,
)

data class SimpleSignUpResult(
    val accountId: AccountId,
)

data class SimpleSignUpPendingMessage(
    val livenessId: String,
    val userContractKey: String,
    val userContractSignature: String,
    val kycDossier: KycDossier? = null,

    val selfieCaptured: Boolean,
    val userContractCaptured: Boolean,
    val deduplicationVerified: Boolean,
    val identityVerified: Boolean,
    val kycDossierGenerated: Boolean,
    val hasPassedRiskAnalysis: Boolean,
    val ecmDocumentsSent: Boolean,
    val accountActivated: Boolean,
    val accountId: String,
)

data class RiskAnalysisException(val reasons: List<RiskAnalysisFailedReason>) : Exception()

enum class RiskAnalysisFailedReason(val action: RiskAnalysisFailedAction, val fraud: Boolean = false) {
    UNDERAGE(RiskAnalysisFailedAction.REJECT),
    HAS_OBIT_INDICATION(RiskAnalysisFailedAction.REJECT, fraud = true),
    FRAUD_LIST_MATCH(RiskAnalysisFailedAction.REJECT, fraud = true),

    NAME_MISMATCH(RiskAnalysisFailedAction.REOPEN),
    BIRTHDATE_MISMATCH(RiskAnalysisFailedAction.REOPEN),

    FACE_MATCH_DUPLICATION(RiskAnalysisFailedAction.UPGRADE),
    FACE_MATCH_FRAUD_INDICATION(RiskAnalysisFailedAction.UPGRADE),
    HAS_SANCTIONS(RiskAnalysisFailedAction.UPGRADE),
    MEDIA_EXPOSED_PERSON(RiskAnalysisFailedAction.UPGRADE),
    POLITICALLY_EXPOSED_PERSON(RiskAnalysisFailedAction.UPGRADE),
    ELDERLY_PERSON(RiskAnalysisFailedAction.UPGRADE),
    DOCUMENT_VALIDATION_REJECTED(RiskAnalysisFailedAction.UPGRADE),
    UNABLE_TO_VALIDATE_DOCUMENT(RiskAnalysisFailedAction.UPGRADE),
    IRREGULAR_DOCUMENT(RiskAnalysisFailedAction.REJECT),
}

enum class RiskAnalysisFailedAction { REJECT, REOPEN, UPGRADE }

fun List<RiskAnalysisFailedReason>.recoverable() = this.all { it.action != RiskAnalysisFailedAction.REJECT }