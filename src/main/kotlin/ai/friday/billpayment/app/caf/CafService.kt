package ai.friday.billpayment.app.caf

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.DocumentScan
import ai.friday.billpayment.app.documentscan.CAFDocumentScanPayloadData
import ai.friday.billpayment.app.documentscan.DocumentScanDigitalSpoofResult
import ai.friday.billpayment.app.documentscan.DocumentScanDigitalSpoofStatus
import ai.friday.billpayment.app.documentscan.DocumentScanFaceResult
import ai.friday.billpayment.app.documentscan.DocumentScanFaceStatus
import ai.friday.billpayment.app.documentscan.DocumentScanId
import ai.friday.billpayment.app.documentscan.DocumentScanImage
import ai.friday.billpayment.app.documentscan.DocumentScanOcrResult
import ai.friday.billpayment.app.documentscan.DocumentScanPayloadData
import ai.friday.billpayment.app.documentscan.DocumentScanRepository
import ai.friday.billpayment.app.documentscan.DocumentScanResult
import ai.friday.billpayment.app.documentscan.DocumentScanTextResult
import ai.friday.billpayment.app.documentscan.GetDocumentScanResultError
import ai.friday.billpayment.app.documentscan.GetImageError
import ai.friday.billpayment.app.documentscan.ImageContent
import ai.friday.billpayment.app.documentscan.UpsertDocumentScanIdError
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.CafAdapterInterface
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.DocumentValidationResponse
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.liveness.LivenessEnrollmentVerification
import ai.friday.billpayment.app.liveness.LivenessErrors
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.liveness.LivenessMatchVerify
import ai.friday.billpayment.app.liveness.LivenessProvider
import ai.friday.morning.json.getObjectMapper
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Property
import io.micronaut.http.HttpRequest
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.Base64
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@ConfigurationProperties("integrations.caf")
data class CafConfiguration @ConfigurationInject constructor(
    val apiToken: String,
    val transactionPath: String,
    val facesPath: String,
    val faceLivenessTemplateId: String,
    val documentLivenessTemplateId: String,
    val documentscopyTemplateId: String,
    val clientId: String,
    val jwtSecret: String,
)

interface CafServiceInterface {
    fun createTransaction(templateId: String, files: List<FileData>, referenceToken: String, document: String? = null, name: String? = null, birthDate: LocalDate? = null): Either<Exception, CreateTransactionResponse>
    fun listTransactions(): Either<Exception, ListTransactionsResponse>
    fun getTransaction(transactionId: String): Either<Exception, GetTransactionResponse>
    fun createLiveness(jwt: String, accountId: AccountId, referenceToken: String): Result<LivenessId>
    fun verifyDuplication(accountId: AccountId): Either<LivenessErrors, LivenessEnrollmentVerification>
    fun retrieveEnrollmentSelfie(livenessId: LivenessId): Either<Exception, ByteArray>
    fun validateDocument(livenessId: LivenessId): Either<Exception, DocumentValidationResponse>
    fun createDocumentscopyTransaction(documentScanId: DocumentScanId, document: String?, name: String?, birthDate: LocalDate?): Either<Exception, String>
    fun registerFace(accountId: AccountId, imageUrl: String): Either<FaceMatchError, Unit>
    fun verifyMatch(livenessId: LivenessId): Either<LivenessErrors, LivenessMatchVerify>
    fun createMatch(jwt: String, accountId: AccountId, referenceToken: String): Either<Exception, LivenessId>
}

@Singleton
@Named("CAF")
class CafService(
    private val cafAdapter: CafAdapterInterface,
    private val accountRegisterRepository: AccountRegisterRepository,
    private val configuration: CafConfiguration,
    private val httpClientToDownloadImages: RxHttpClient,
    private val messagePublisher: MessagePublisher,
    @Property(name = "sqs.faceRegistrationQueueName") private val faceRegistrationQueue: String,
) : CafServiceInterface, DocumentScanRepository {
    override fun createTransaction(templateId: String, files: List<FileData>, referenceToken: String, document: String?, name: String?, birthDate: LocalDate?): Either<Exception, CreateTransactionResponse> {
        val markers = append("templateId", templateId)

        return cafAdapter.createTransaction(templateId, files, referenceToken, document, name, birthDate).fold(
            ifLeft = {
                logger.error(markers, "CafService#createTransaction", it)
                it.left()
            },
            ifRight = { response ->
                logger.info(
                    markers.andAppend("transactionId", response.id),
                    "CafService#createTransaction",
                )
                response.right()
            },
        )
    }

    override fun listTransactions(): Either<Exception, ListTransactionsResponse> {
        return cafAdapter.listTransactions().fold(
            ifLeft = {
                logger.error("CafService#listTransactions", it)
                it.left()
            },
            ifRight = { response ->
                logger.info(
                    append("totalItems", response.totalItems),
                    "CafService#listTransactions",
                )
                response.right()
            },
        )
    }

    override fun getTransaction(transactionId: String): Either<Exception, GetTransactionResponse> {
        val markers = append("transactionId", transactionId)

        return cafAdapter.getTransaction(transactionId).fold(
            ifLeft = {
                logger.error(markers, "CafService#getTransaction", it)
                it.left()
            },
            ifRight = { response ->
                logger.info(
                    markers.andAppend("status", response.status),
                    "CafService#getTransaction",
                )
                response.right()
            },
        )
    }

    override fun createLiveness(jwt: String, accountId: AccountId, referenceToken: String): Result<LivenessId> = runCatching {
        val markers = append("accountId", accountId.value).andAppend("jwtSize", jwt.length)
        val logName = "CafService#createLiveness"
        try {
            val sessionData = decodeJwtCaf(jwt, configuration.jwtSecret)

            if (!sessionData.isAlive) {
                val exception = IllegalStateException("Session data indicates user is not alive")
                logger.error(markers, logName, exception)
                throw exception
            }

            val files = listOf(
                FileData(
                    data = sessionData.imageUrl,
                    type = "SELFIE",
                ),
            )

            val accountRegister = accountRegisterRepository.findByAccountId(accountId)

            if (accountRegister.document == null) {
                val exception = IllegalStateException("AccountRegister for accountId $accountId does not have a document")
                logger.error(markers, logName, exception)
                throw exception
            }

            cafAdapter.createTransaction(configuration.faceLivenessTemplateId, files, referenceToken, accountRegister.document.value, accountRegister.nickname, accountRegister.birthDate).fold(
                ifLeft = { exception ->
                    logger.error(markers, logName, exception)
                    throw exception
                },
                ifRight = { response ->
                    if (response.id == null) {
                        val exception = IllegalStateException("CAF response does not contain transaction ID")
                        logger.error(markers, logName, exception)
                        throw exception
                    }

                    try {
                        val updatedAccountRegister = accountRegister.copy(
                            livenessId = LivenessId(response.id, LivenessProvider.CAF),
                        )
                        accountRegisterRepository.save(updatedAccountRegister)

                        logger.info(
                            markers.andAppend("transactionId", response.id)
                                .andAppend("sessionId", sessionData.sessionId)
                                .andAppend("livenessId", response.id)
                                .andAppend("provider", "CAF"),
                            "CafService#createLiveness - success, livenessId saved to accountRegister",
                        )

                        val faceRegistrationRequest = FaceRegistrationRequest(
                            personId = accountId.value,
                            imageUrl = files.first().data,
                        )

                        messagePublisher.sendMessage(faceRegistrationQueue, faceRegistrationRequest, 5)

                        LivenessId(response.id, LivenessProvider.CAF)
                    } catch (e: Exception) {
                        logger.error(
                            markers.andAppend("transactionId", response.id)
                                .andAppend("livenessId", response.id),
                            "CafService#createLiveness - failed to save livenessId to accountRegister",
                            e,
                        )
                        throw e
                    }
                },
            )
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            throw e
        }
    }

    override fun upsertDocumentScanId(payload: DocumentScanPayloadData): Either<UpsertDocumentScanIdError, DocumentScan> {
        return when (payload) {
            is CAFDocumentScanPayloadData -> return createDocument(payload.jwt, payload.accountId, payload.referenceToken).fold(
                onSuccess = { documentScan -> documentScan.right() },
                onFailure = { exception -> UpsertDocumentScanIdError.Unexpected(exception).left() },
            )
            else -> UpsertDocumentScanIdError.IllegalAccountRole.left()
        }
    }

    override fun getResult(id: DocumentScanId): Either<GetDocumentScanResultError, DocumentScanResult> {
        val markers = append("documentScanId", id.value).andAppend("provider", id.provider.toString())
        val logName = "CafService#getResult"

        return try {
            cafAdapter.getTransaction(id.value).fold(
                ifLeft = { exception ->
                    logger.error(markers, logName, exception)
                    GetDocumentScanResultError.Unexpected(exception).left()
                },
                ifRight = { transactionResponse ->
                    if (transactionResponse.status == "PROCESSING") {
                        logger.warn(markers.andAppend("status", transactionResponse.status).andAppend("context", "document scan incomplete"), logName)
                        GetDocumentScanResultError.Incomplete(id).left()
                    } else {
                        val sections = transactionResponse.sections ?: run {
                            val exception = IllegalStateException("No sections found in transaction response")
                            logger.error(markers, logName, exception)
                            return GetDocumentScanResultError.Unexpected(exception).left()
                        }

                        val digitalSpoofResult = mapDocumentLivenessToDigitalSpoof(sections.documentLiveness)
                        val faceResult = mapDocumentscopyToFaceResult(sections.documentscopy)
                        val textResult = DocumentScanTextResult.NotPerformed // CAF doesn't provide text analysis in this format
                        val ocrResult = mapCpfDataToOcrResult(sections.pfCpfData, sections.cpf)

                        val result = DocumentScanResult(
                            digitalSpoof = digitalSpoofResult,
                            face = faceResult,
                            text = textResult,
                            ocr = ocrResult,
                        )

                        logger.info(markers, logName)
                        result.right()
                    }
                },
            )
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            GetDocumentScanResultError.Unexpected(e).left()
        }
    }

    override fun createDocumentscopyTransaction(documentScanId: DocumentScanId, document: String?, name: String?, birthDate: LocalDate?): Either<Exception, String> {
        val actualReferenceToken = documentScanId.referenceToken ?: return IllegalStateException("DocumentScanId must have a referenceToken for documentscopy").left()
        val markers = append("documentScanId", documentScanId.value).andAppend("referenceToken", actualReferenceToken)
        val logName = "CafService#createDocumentscopyTransaction"

        return try {
            // Buscar a transação do documento já processado para obter os links
            val transactionResponse = cafAdapter.getTransaction(documentScanId.value).getOrElse { exception ->
                logger.error(markers.andAppend("documentScanId", documentScanId.value), logName, exception)
                return exception.left()
            }

            val images = transactionResponse.images ?: run {
                val exception = IllegalStateException("No images found in document scan transaction")
                logger.error(markers, logName, exception)
                return exception.left()
            }

            val documentType = transactionResponse.type?.uppercase() ?: run {
                val exception = IllegalStateException("No document type found in document scan transaction")
                logger.error(markers, logName, exception)
                return exception.left()
            }

            val files = mutableListOf<FileData>()

            images.front?.let { frontUrl ->
                files.add(
                    FileData(
                        data = frontUrl,
                        type = "${documentType}_FRONT",
                    ),
                )
            }

            images.back?.let { backUrl ->
                files.add(
                    FileData(
                        data = backUrl,
                        type = "${documentType}_BACK",
                    ),
                )
            }

            if (files.isEmpty()) {
                val exception = IllegalStateException("No document image URLs found")
                logger.error(markers, logName, exception)
                return exception.left()
            }

            // Criar transação de documentoscopia
            val createResponse = createTransaction(
                templateId = configuration.documentscopyTemplateId,
                files = files,
                referenceToken = actualReferenceToken,
                document = document,
                name = name,
                birthDate = birthDate,
            ).getOrElse { exception ->
                logger.error(markers, logName, exception)
                return exception.left()
            }

            val transactionId = createResponse.id ?: run {
                val exception = IllegalStateException("CAF documentscopy transaction response does not contain transaction ID")
                logger.error(markers, logName, exception)
                return exception.left()
            }

            logger.info(
                markers.andAppend("documentscopyTransactionId", transactionId),
                logName,
            )

            transactionId.right()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            e.left()
        }
    }

    override fun registerFace(accountId: AccountId, imageUrl: String): Either<FaceMatchError, Unit> {
        val markers = append("accountId", accountId.value)
        val logName = "CafService#registerFace"

        return try {
            // Buscar o accountRegister para obter o livenessId
            val accountRegister = accountRegisterRepository.findByAccountId(accountId)
            val livenessId = accountRegister.livenessId

            if (livenessId == null || livenessId.provider != LivenessProvider.CAF) {
                logger.warn(markers.andAppend("livenessProvider", livenessId?.provider?.toString()), logName)
                return FaceMatchError.TransactionNotFound.left()
            }

            cafAdapter.getTransaction(livenessId.value).fold(
                ifLeft = { exception ->
                    logger.error(markers.andAppend("livenessId", livenessId.value), logName, exception)
                    FaceMatchError.Error(exception).left()
                },
                ifRight = { transactionResponse ->
                    when (transactionResponse.status) {
                        "PROCESSING" -> {
                            logger.warn(markers.andAppend("livenessId", livenessId.value).andAppend("status", transactionResponse.status), logName)
                            FaceMatchError.TransactionProcessing.left()
                        }

                        else -> {
                            cafAdapter.registerFace(accountId.value, imageUrl).fold(
                                ifLeft = { exception ->
                                    logger.error(markers, logName, exception)
                                    FaceMatchError.Error(exception).left()
                                },
                                ifRight = { response ->
                                    logger.info(
                                        markers.andAppend("livenessId", livenessId.value),
                                        logName,
                                    )
                                    Unit.right()
                                },
                            )
                        }
                    }
                },
            )
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            FaceMatchError.Error(e).left()
        }
    }

    override fun verifyMatch(livenessId: LivenessId): Either<LivenessErrors, LivenessMatchVerify> {
        val markers = append("livenessId", livenessId.value)
        val logName = "CafService#verifyMatch"

        return try {
            val matchLivenessId = livenessId.matchLivenessId
            if (matchLivenessId == null) {
                logger.warn(markers.andAppend("context", "no matchLivenessId found"), logName)
                return LivenessErrors.MatchUnavailable.left()
            }

            cafAdapter.getFaceAuthenticationAttempt(matchLivenessId).fold(
                ifLeft = { exception ->
                    logger.error(markers.andAppend("attemptId", matchLivenessId), logName, exception)
                    LivenessErrors.Error(exception).left()
                },
                ifRight = { response ->
                    val isMatch = response.data.isMatch
                    val attempt = 1 // CAF doesn't provide attempt count, defaulting to 1

                    val accountId = AccountId(response.data.personId)

                    val result = LivenessMatchVerify(
                        livenessId = livenessId,
                        accountId = accountId,
                        match = isMatch,
                        attempt = attempt,
                    )

                    logger.info(
                        markers.andAppend("attemptId", matchLivenessId)
                            .andAppend("accountId", accountId.value)
                            .andAppend("match", isMatch)
                            .andAppend("similarity", response.data.similarity),
                        logName,
                    )

                    result.right()
                },
            )
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            LivenessErrors.Error(e).left()
        }
    }

    override fun createMatch(jwt: String, accountId: AccountId, referenceToken: String): Either<Exception, LivenessId> {
        val markers = append("accountId", accountId.value).andAppend("jwtSize", jwt.length)
        val logName = "CafService#createMatch"
        try {
            val sessionData = decodeJwtCaf(jwt, configuration.jwtSecret)

            val accountRegister = accountRegisterRepository.findByAccountId(accountId)

            if (sessionData.faceAuthenticationAttemptId == null) {
                val exception = IllegalStateException("Session data does not contain faceAuthenticationAttemptId")
                logger.error(markers, logName, exception)
                return exception.left()
            }

            val livenessId = accountRegister.livenessId

            if (livenessId == null || livenessId.provider != LivenessProvider.CAF) {
                val exception = IllegalStateException("AccountRegister for accountId $accountId does not have a valid CAF livenessId")
                logger.error(markers, logName, exception)
                return exception.left()
            }

            val updatedLivenessId = livenessId.copy(matchLivenessId = sessionData.faceAuthenticationAttemptId)

            accountRegisterRepository.save(accountRegister.copy(livenessId = updatedLivenessId))

            return updatedLivenessId.right()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return e.left()
        }
    }

    override fun getImage(id: DocumentScanId): Either<GetImageError, DocumentScanImage> {
        val markers = append("documentScanId", id.value).andAppend("provider", id.provider.toString())
        val logName = "CafService#getImage"

        return try {
            cafAdapter.getTransaction(id.value).fold(
                ifLeft = { exception ->
                    logger.error(markers, logName, exception)
                    GetImageError.Unexpected(exception).left()
                },
                ifRight = { transactionResponse ->
                    if (transactionResponse.status == "PROCESSING") {
                        logger.warn(markers.andAppend("status", transactionResponse.status).andAppend("context", "document scan incomplete"), logName)
                        GetImageError.Incomplete(id).left()
                    } else {
                        val images = transactionResponse.images ?: run {
                            val exception = IllegalStateException("No images found in transaction response")
                            logger.error(markers, logName, exception)
                            return GetImageError.Unexpected(exception).left()
                        }

                        try {
                            val frontUrl = images.front
                            val backUrl = images.back

                            if (frontUrl != null && backUrl != null) {
                                // Double-sided document (RG, CNH)
                                val frontBytes = downloadFile(frontUrl)
                                val backBytes = downloadFile(backUrl)
                                val frontContent = ImageContent(frontBytes, "jpg")
                                val backContent = ImageContent(backBytes, "jpg")
                                DocumentScanImage.DoubleSided(frontContent, backContent).right()
                            } else if (frontUrl != null) {
                                // Single-sided document
                                val frontBytes = downloadFile(frontUrl)
                                val frontContent = ImageContent(frontBytes, "jpg")
                                DocumentScanImage.SingleSided(frontContent).right()
                            } else {
                                val exception = IllegalStateException("No document images found")
                                logger.error(markers, logName, exception)
                                GetImageError.Unexpected(exception).left()
                            }
                        } catch (e: Exception) {
                            logger.error(markers.andAppend("context", "error downloading images"), logName, e)
                            GetImageError.Unexpected(e).left()
                        }
                    }
                },
            )
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            GetImageError.Unexpected(e).left()
        }
    }

    override fun verifyDuplication(accountId: AccountId): Either<LivenessErrors, LivenessEnrollmentVerification> {
        val markers = append("accountId", accountId.value)
        val logName = "CafService#verifyDuplication"

        return try {
            val accountRegister = accountRegisterRepository.findByAccountId(accountId)
            val livenessId = accountRegister.livenessId

            if (livenessId == null || livenessId.provider != LivenessProvider.CAF) {
                logger.warn(markers.andAppend("livenessProvider", livenessId?.provider?.toString()), logName)
                return Either.Right(LivenessEnrollmentVerification())
            }

            cafAdapter.getTransaction(livenessId.value).fold(
                ifLeft = { exception ->
                    logger.error(markers.andAppend("livenessId", livenessId.value), logName, exception)
                    return Either.Left(LivenessErrors.Error(exception))
                },
                ifRight = { transactionResponse ->
                    val sections = transactionResponse.sections

                    if (sections == null) {
                        logger.warn(markers.andAppend("livenessId", livenessId.value), logName)
                        return Either.Right(LivenessEnrollmentVerification())
                    }

                    val fraudAccountIds = mutableSetOf<AccountId>()

                    sections.privateFaceset?.data?.let { privateFacesetData ->
                        if (privateFacesetData.suspect == true) {
                            fraudAccountIds.add(accountId)
                        }
                    }

                    sections.sharedFaceset?.data?.let { sharedFacesetData ->
                        if (sharedFacesetData.suspect == true) {
                            fraudAccountIds.add(accountId)
                        }
                    }

                    val fraudResult = LivenessEnrollmentVerification.Result.create(fraudAccountIds.toList())
                    val duplicationsResult = LivenessEnrollmentVerification.Result.create(emptyList())

                    logger.info(
                        markers.andAppend("livenessId", livenessId.value)
                            .andAppend("fraudIndicationsFound", fraudAccountIds.size)
                            .andAppend("privateFacesetSuspect", sections.privateFaceset?.data?.suspect)
                            .andAppend("sharedFacesetSuspect", sections.sharedFaceset?.data?.suspect),
                        logName,
                    )

                    Either.Right(
                        LivenessEnrollmentVerification(
                            duplications = duplicationsResult,
                            fraudIndications = fraudResult,
                        ),
                    )
                },
            )
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            Either.Left(LivenessErrors.Error(e))
        }
    }

    override fun retrieveEnrollmentSelfie(livenessId: LivenessId): Either<Exception, ByteArray> {
        val markers = append("livenessId", livenessId.value).andAppend("provider", livenessId.provider.toString())
        val logName = "CafService#retrieveEnrollmentSelfie"

        return cafAdapter.retrieveEnrollmentSelfie(livenessId.value).getOrElse { exception ->
            logger.error(markers, logName, exception)
            return exception.left()
        }.right()
    }

    override fun validateDocument(livenessId: LivenessId): Either<Exception, DocumentValidationResponse> {
        return cafAdapter.validateDocument(livenessId)
    }

    fun generateJwtToken(payload: CAFJwtPayload, secret: String): String {
        val header = CAFJwtHeader(
            alg = "HS256",
            typ = "JWT",
        )

        val encodedHeader = getObjectMapper().writeValueAsString(header)
            .toBase64()
            .replace("=", "")
            .replace("+", "-")
            .replace("/", "_")

        val encodedPayload = getObjectMapper().writeValueAsString(payload)
            .toBase64()
            .replace("=", "")
            .replace("+", "-")
            .replace("/", "_")

        val data = "$encodedHeader.$encodedPayload"

        val signature = signHmacSha256(data, secret)
        val encodedSignature = signature
            .replace("=", "")
            .replace("+", "-")
            .replace("/", "_")

        return "$data.$encodedSignature"
    }

    private fun signHmacSha256(data: String, secret: String): String {
        val secretKeySpec = SecretKeySpec(secret.toByteArray(Charsets.UTF_8), "HmacSHA256")
        val mac = Mac.getInstance("HmacSHA256")
        mac.init(secretKeySpec)
        val signature = mac.doFinal(data.toByteArray(Charsets.UTF_8))
        return Base64.getEncoder().encodeToString(signature)
    }

    private fun String.toBase64(): String {
        return Base64.getEncoder().encodeToString(this.toByteArray(Charsets.UTF_8))
    }

    private fun createDocument(jwt: String, accountId: AccountId, referenceToken: String): Result<DocumentScan> = runCatching {
        val markers = append("accountId", accountId.value).andAppend("jwtSize", jwt.length)
        val logName = "CafService#createDocument"

        val accountRegister = accountRegisterRepository.findByAccountId(accountId)

        try {
            val documentSessionData = decodeDocumentJwtCaf(jwt, configuration.jwtSecret)

            val files = documentSessionData.captures.map { capture ->
                val fileType = when (capture.scannedLabel.lowercase()) {
                    "rg_front", "rg_new_front" -> "RG_FRONT"
                    "rg_back", "rg_new_back" -> "RG_BACK"
                    "cnh_front", "new_cnh_front" -> "CNH_FRONT"
                    "cnh_back", "new_cnh_back" -> "CNH_BACK"
                    else -> throw IllegalArgumentException("Unsupported document type: ${capture.scannedLabel}")
                }

                FileData(
                    data = capture.imageUrl,
                    type = fileType,
                )
            }

            cafAdapter.createTransaction(configuration.documentLivenessTemplateId, files, referenceToken, accountRegister.document?.value, accountRegister.nickname, accountRegister.birthDate).fold(
                ifLeft = { exception ->
                    logger.error(markers, logName, exception)
                    throw exception
                },
                ifRight = { response ->
                    if (response.id == null) {
                        val exception = IllegalStateException("CAF response does not contain transaction ID")
                        logger.error(markers, logName, exception)
                        throw exception
                    }

                    try {
                        val documentScan = DocumentScan(
                            documentType = mapJwtDocumentTypeToIntegrations(documentSessionData.documentType),
                            documentScanId = DocumentScanId(response.id, LivenessProvider.CAF, referenceToken, documentscopyTransactionId = null),
                        )

                        val updatedAccountRegister = accountRegister.copy(
                            documentScan = documentScan,
                        )
                        accountRegisterRepository.save(updatedAccountRegister)

                        logger.info(
                            markers.andAppend("transactionId", response.id)
                                .andAppend("trackingId", documentSessionData.trackingId)
                                .andAppend("referenceToken", referenceToken)
                                .andAppend("documentScanId", response.id)
                                .andAppend("provider", "CAF")
                                .andAppend("documentType", documentSessionData.documentType),
                            logName,
                        )

                        documentScan
                    } catch (e: Exception) {
                        logger.error(
                            markers.andAppend("transactionId", response.id)
                                .andAppend("documentScanId", response.id)
                                .andAppend("context", "failed to save documentScanId to accountRegister"),
                            logName,
                            e,
                        )
                        throw e
                    }
                },
            )
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            throw e
        }
    }

    private fun decodeJwtCaf(jwt: String, secret: String): SessionData {
        val markers = append("jwt", jwt)
        try {
            val parts = jwt.split(".")
            if (parts.size != 3) {
                throw IllegalArgumentException("Invalid JWT format")
            }

            val header = parts[0]
            val payload = parts[1]
            val signature = parts[2]

            val expectedSignature = createSignature("$header.$payload", secret)
            if (!verifySignature(signature, expectedSignature)) {
                throw IllegalStateException("Invalid JWT signature")
            }

            val payloadJson = String(Base64.getUrlDecoder().decode(payload))
            val sessionData = parseObjectFrom<SessionData>(payloadJson)

            markers.andAppend("sessionData", sessionData)
            logger.info(markers, "InternalLivenessController#decodeJwtToCafTransactionRequest")

            return sessionData
        } catch (e: Exception) {
            logger.error(markers, "InternalLivenessController#decodeJwtToCafTransactionRequest", e)
            throw IllegalStateException("Failed to decode JWT", e)
        }
    }

    private fun createSignature(data: String, secret: String): String {
        val algorithm = "HmacSHA256"
        val mac = Mac.getInstance(algorithm)
        val secretKeySpec = SecretKeySpec(secret.toByteArray(), algorithm)
        mac.init(secretKeySpec)
        val signature = mac.doFinal(data.toByteArray())
        return Base64.getUrlEncoder().withoutPadding().encodeToString(signature)
    }

    private fun verifySignature(providedSignature: String, expectedSignature: String): Boolean {
        return providedSignature == expectedSignature
    }

    private fun decodeDocumentJwtCaf(jwt: String, secret: String): DocumentSessionData {
        val markers = append("jwt", jwt)
        try {
            val parts = jwt.split(".")
            if (parts.size != 3) {
                throw IllegalArgumentException("Invalid JWT format")
            }

            val header = parts[0]
            val payload = parts[1]
            val signature = parts[2]

            val expectedSignature = createSignature("$header.$payload", secret)
            if (!verifySignature(signature, expectedSignature)) {
                throw IllegalStateException("Invalid JWT signature")
            }

            val payloadJson = String(Base64.getUrlDecoder().decode(payload))
            val documentSessionData = parseObjectFrom<DocumentSessionData>(payloadJson)

            markers.andAppend("documentSessionData", documentSessionData)
            logger.info(markers, "CafService#decodeDocumentJwtCaf")

            return documentSessionData
        } catch (e: Exception) {
            logger.error(markers, "CafService#decodeDocumentJwtCaf", e)
            throw IllegalStateException("Failed to decode document JWT", e)
        }
    }

    private fun mapDocumentTypeToCaf(jwtDocumentType: String): String {
        return when (jwtDocumentType.uppercase()) {
            "CNH" -> "cnh"
            "CNHV2" -> "cnh_new"
            "RG" -> "rg"
            "NEWRG" -> "rg_new"
            "PASSPORT" -> "passport"
            "CTPS" -> "ctps"
            "RNE" -> "rne"
            "RNM" -> "rnm"
            "CIN" -> "cin"
            "CRLV" -> "crlv"
            "CRLV_NEW" -> "crlv_new"
            else -> "generic"
        }
    }

    private fun mapJwtDocumentTypeToIntegrations(jwtDocumentType: String): DocumentType {
        return when (jwtDocumentType.lowercase()) {
            "cnh" -> DocumentType.CNH
            "new_cnh" -> DocumentType.CNHV2
            "rg" -> DocumentType.RG
            "rg_new" -> DocumentType.NEWRG
            "blank",
            "generic",
            "rne",
            "rnm",
            "ctps",
            "passport",
            "crlv",
            "crlv_new",
            "cin",
            -> throw IllegalArgumentException("Document type $jwtDocumentType not supported")
            else -> throw IllegalArgumentException("Unknown document type $jwtDocumentType")
        }
    }

    private fun mapDocumentLivenessToDigitalSpoof(documentLiveness: DocumentLivenessSection?): DocumentScanDigitalSpoofResult {
        return if (documentLiveness?.statusCode == "01") {
            DocumentScanDigitalSpoofResult(DocumentScanDigitalSpoofStatus.LIKELY_PHYSICAL_ID)
        } else {
            DocumentScanDigitalSpoofResult(DocumentScanDigitalSpoofStatus.CANNOT_CONFIRM_PHYSICAL_ID)
        }
    }

    private fun mapDocumentscopyToFaceResult(documentscopy: DocumentscopySection?): DocumentScanFaceResult {
        return when (documentscopy?.status) {
            "APPROVED" -> {
                if (documentscopy.fraud == false) {
                    DocumentScanFaceResult.Found(DocumentScanFaceStatus.LIKELY_ORIGINAL_FACE)
                } else {
                    DocumentScanFaceResult.Found(DocumentScanFaceStatus.CANNOT_CONFIRM_ID_IS_AUTHENTIC)
                }
            }
            else -> DocumentScanFaceResult.NotFound
        }
    }

    private fun mapCpfDataToOcrResult(pfCpfData: PfCpfDataSection?, cpf: CpfSection?): DocumentScanOcrResult {
        return if (pfCpfData?.statusCode == "01" || cpf?.statusCode == "00") {
            DocumentScanOcrResult.Matched
        } else {
            DocumentScanOcrResult.NotMatched
        }
    }

    private fun downloadFile(url: String): ByteArray {
        val markers = append("url", url)
        return try {
            val request = HttpRequest.GET<ByteArray>(url)
            val call = httpClientToDownloadImages.retrieve(request, ByteArray::class.java)
            call.blockingFirst().also { logger.info(markers, "CafService#downloadFile") }
        } catch (e: Exception) {
            logger.error("Error downloading file from S3: $url", e)
            throw IllegalStateException("Failed to download file from S3", e)
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(CafService::class.java)
    }
}

data class FileData(
    val data: String,
    val type: String,
)

data class CreateTransactionResponse(
    val requestId: String,
    val id: String?,
    val status: String?,
)

data class ListTransactionsResponse(
    val requestId: String,
    val items: List<TransactionItem>,
    val totalItems: Int,
)

data class TransactionItem(
    val id: String,
    val status: String,
    val createdAt: LocalDateTime,
    val data: TransactionData?,
)

data class TransactionData(
    val cpf: String?,
    val birthDate: String?,
    val name: String?,
)

data class GetTransactionResponse(
    val requestId: String,
    val id: String,
    val status: String,
    val type: String?,
    val images: Images?,
    val sections: Sections?,
    val history: List<HistoryItem>?,
    val metadata: Metadata?,
    val templateId: String?,
    val createdAt: String?,
    val fraud: Boolean?,
    val fraudScore: FraudScore?,
    val statusReasons: List<StatusReason>?,
    val attributes: Map<String, Any>?,
    val files: List<Any>?,
)

data class Images(
    val selfie: String?,
    val front: String?,
    val back: String?,
)

data class Sections(
    val liveness: LivenessSection?,
    val facematch: FacematchSection?,
    val officialData: OfficialDataSection?,
    val document: DocumentSection?,
    val documentscopy: DocumentscopySection?,
    val facialbiometrics: FacialbiometricsSection?,
    val privateFaceset: PrivateFacesetSection?,
    val sharedFaceset: SharedFacesetSection?,
    val documentLiveness: DocumentLivenessSection?,
    val globalDocumentVerification: GlobalDocumentVerificationSection?,
    val ocr: OcrSection?,
    val pfCpfData: PfCpfDataSection?,
    val cpf: CpfSection?,
)

data class LivenessSection(
    val statusCode: String?,
    val data: LivenessData?,
)

data class LivenessData(
    val status: String?,
    val message: String?,
    val isAlive: Boolean?,
    val info: LivenessInfo?,
)

data class LivenessInfo(
    val probability: Double?,
    val openEyesProbability: Double?,
)

data class FacematchSection(
    val statusCode: String?,
    val confidence: Double?,
    val identical: Boolean?,
)

data class OfficialDataSection(
    val source: String?,
    val code: String?,
    val confidence: Double?,
    val probability: OfficialDataProbability?,
    val queryDate: String?,
)

enum class OfficialDataProbability {
    @JsonProperty("Altíssima probabilidade")
    VERY_HIGH,

    @JsonProperty("Alta probabilidade")
    HIGH,

    @JsonProperty("Baixa probabilidade")
    LOW,

    @JsonProperty("Baixíssima probabilidade")
    VERY_LOW,
}

data class HistoryItem(
    val type: String?,
    val status: String?,
    val date: String?,
)

data class Metadata(
    val tenantId: String?,
    val origin: String?,
    val templateOrigin: String?,
)

data class FraudScore(
    val total: Int?,
)

data class StatusReason(
    val category: String?,
    val code: String?,
    val status: String?,
    val resultStatus: String?,
)

data class DocumentSection(
    val statusCode: String?,
    val data: DocumentData?,
)

data class DocumentData(
    val documentType: String?,
    val documentNumber: String?,
    val name: String?,
    val birthDate: String?,
    val cpf: String?,
    val rg: String?,
    val issueDate: String?,
    val expirationDate: String?,
    val fatherName: String?,
    val motherName: String?,
    val status: String?,
)

data class DocumentscopySection(
    val status: String?,
    val fraud: Boolean?,
    val evaluation: List<Any>?,
    val reviewDate: String?,
)

data class FacialbiometricsSection(
    val statusCode: String?,
    val data: FacialbiometricsData?,
)

data class FacialbiometricsData(
    val template: String?,
    val quality: Double?,
    val status: String?,
)

data class PrivateFacesetSection(
    val statusCode: String?,
    val data: PrivateFacesetData?,
)

data class PrivateFacesetData(
    val suspect: Boolean?,
    val faceMatches: List<PrivateFaceMatch>?,
)

data class PrivateFaceMatch(
    val similarity: String?,
    val verified: Boolean?,
    val faceset: FacesetInfo?,
)

data class FacesetInfo(
    val id: String?,
    val createdAt: String?,
    val description: String?,
)

data class SharedFacesetSection(
    val statusCode: String?,
    val data: SharedFacesetData?,
)

data class SharedFacesetData(
    val suspect: Boolean?,
    val faceMatches: List<SharedFaceMatch>?,
)

data class SharedFaceMatch(
    val similarity: String?,
    val verified: Boolean?,
)

data class DocumentLivenessSection(
    val statusCode: String?,
    val data: DocumentLivenessData?,
)

data class DocumentLivenessData(
    val isColoredCopy: Boolean?,
    val isGrayscaleCopy: Boolean?,
    val isScreenPhoto: Boolean?,
    val status: String?,
    val message: String?,
)

data class GlobalDocumentVerificationSection(
    val statusCode: String?,
    val data: GlobalDocVerificationData?,
)

data class GlobalDocVerificationData(
    val documentCategory: String?,
    val documentType: String?,
    val documentNumber: String?,
    val name: String?,
    val sex: String?,
    val birthDate: String?,
    val birthPlace: String?,
    val nationality: String?,
    val nationalityCode: String?,
    val address: String?,
    val issueState: String?,
    val issueStateCode: String?,
    val authority: String?,
    val issueDate: String?,
    val expirationDate: String?,
    val validations: DocumentValidations?,
)

data class DocumentValidations(
    val overallStatus: Int?,
    val authenticityCheck: AuthenticityCheck?,
    val documentType: DocumentTypeValidation?,
    val textFieldsCheck: TextFieldsCheck?,
    val imageQualityCheck: ImageQualityCheck?,
)

data class AuthenticityCheck(
    val status: String?,
    val score: Double?,
)

data class DocumentTypeValidation(
    val status: String?,
    val confidence: Double?,
)

data class TextFieldsCheck(
    val status: String?,
    val fields: List<String>?,
)

data class ImageQualityCheck(
    val status: String?,
    val score: Double?,
)

data class OcrSection(
    val headerRgFront: String?,
    val federativeUnit: String?,
    val issuingAuthorityHeader: String?,
    val institute: String?,
    val department: String?,
    val documentTypeRgFront: String?,
    val rg: String?,
    val rgMask: String?,
    val via: String?,
    val issueDate: String?,
    val name: String?,
    val fatherName: String?,
    val parentsSeparator: String?,
    val motherName: String?,
    val birthPlace: String?,
    val birthDate: String?,
    val referenceDocument: String?,
    val cpf: String?,
    val cpfMask: String?,
    val graphicName: String?,
    val footerRgBack: String?,
    val issuingAuthority: String?,
    val issueState: String?,
    val lawRgBack: String?,
    val rgIssuingAuthority: String?,
    val rgIssueState: String?,
)

data class PfCpfDataSection(
    val statusCode: String?,
    val data: PfCpfData?,
)

data class PfCpfData(
    val taxIdNumber: String?,
    val name: String?,
    val socialName: String?,
    val taxIdStatus: String?,
    val birthDate: String?,
    val fallback: String?,
)

data class CpfSection(
    val statusCode: String?,
    val registrationStatusCode: String?,
    val registrationStatusMessage: String?,
    val name: String?,
    val socialName: String?,
    val birthDate: String?,
    val issueDate: String?,
    val deathYear: String?,
    val deathMessage: String?,
)

enum class TransactionStatus {
    APPROVED,
    REJECTED,
    PENDING,
}

data class CAFJwtPayload(
    val iss: String,
    val exp: Long,
)

data class CAFJwtHeader(
    val alg: String,
    val typ: String,
)

data class SessionData(
    val sessionId: String,
    val personId: String,
    val isAlive: Boolean,
    val isMatch: Boolean? = null,
    val faceAuthenticationAttemptId: String? = null,
    val imageUrl: String,
    val createdAt: String,
    val iat: Long,
)

data class DocumentSessionData(
    val captures: List<CaptureData>,
    val documentType: String,
    val trackingId: String,
    val iat: Long,
)

data class CaptureData(
    val scannedLabel: String,
    val imageUrl: String,
)

fun AccountRegisterData.isCafTransaction() = livenessId != null && livenessId.provider == LivenessProvider.CAF

data class FaceRegistrationRequest(
    val personId: String,
    val imageUrl: String,
)

data class FaceRegistrationResponse(
    val requestId: String,
    val attemptId: String,
)

data class FaceAuthenticationAttemptResponse(
    val requestId: String,
    val data: FaceAttemptDetails,
)

data class FaceAttemptDetails(
    val id: String,
    val createdAt: String,
    val personId: String,
    val sourceIp: String,
    val registeredFaceImageUrl: String,
    val capturedFaceImageUrl: String,
    val isMatch: Boolean,
    val similarity: Double,
)

sealed class FaceMatchError : PrintableSealedClassV2() {
    data object TransactionNotFound : FaceMatchError()
    data object TransactionProcessing : FaceMatchError()
    data class Error(val exception: Throwable) : FaceMatchError()
}

data class FaceAuthenticationResult(
    val status: String,
    val similarity: Double?,
    val isMatch: Boolean?,
)