package ai.friday.billpayment.adapters.caf

import ai.friday.billpayment.app.caf.CafConfiguration
import ai.friday.billpayment.app.caf.CreateTransactionResponse
import ai.friday.billpayment.app.caf.FaceAuthenticationAttemptResponse
import ai.friday.billpayment.app.caf.FaceRegistrationRequest
import ai.friday.billpayment.app.caf.FileData
import ai.friday.billpayment.app.caf.GetTransactionResponse
import ai.friday.billpayment.app.caf.ListTransactionsResponse
import ai.friday.billpayment.app.caf.OfficialDataProbability
import ai.friday.billpayment.app.integrations.CafAdapterInterface
import ai.friday.billpayment.app.integrations.DocumentValidationResponse
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.right
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
open class CafAdapter(
    @Client("\${integrations.caf.host}")
    private val httpClient: RxHttpClient,
    private val configuration: CafConfiguration,
) : CafAdapterInterface {

    override fun createTransaction(templateId: String, files: List<FileData>, referenceToken: String, document: String?, name: String?, birthDate: LocalDate?): Either<Exception, CreateTransactionResponse> {
        val markers = append("templateId", templateId)

        val httpRequest = HttpRequest.POST(
            "${configuration.transactionPath}?origin=TRUST",
            CreateTransactionTO(
                templateId = templateId,
                files = files,
                referenceToken = referenceToken,
                attributes = CafAttributes(cpf = document, name = name, birthDate = birthDate?.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))),
            ),
        ).contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON_TYPE).bearerAuth(configuration.apiToken)

        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(CreateTransactionResponse::class.java),
            Argument.STRING,
        )

        return try {
            val response = call.firstOrError().blockingGet()
            logger.info(markers, "CafAdapter#createTransaction - success")
            Either.Right(response)
        } catch (err: Exception) {
            logger.error(markers, "CafAdapter#createTransaction", err)
            Either.Left(err)
        }
    }

    override fun listTransactions(): Either<Exception, ListTransactionsResponse> {
        val httpRequest = HttpRequest.GET<ListTransactionsResponse>(configuration.transactionPath)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE).bearerAuth(configuration.apiToken)

        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(ListTransactionsResponse::class.java),
            Argument.STRING,
        )

        return try {
            val response = call.firstOrError().blockingGet()
            logger.info(append("totalItems", response.totalItems), "CafAdapter#listTransactions")
            Either.Right(response)
        } catch (err: Exception) {
            logger.error("CafAdapter#listTransactions", err)
            Either.Left(err)
        }
    }

    override fun getTransaction(transactionId: String): Either<Exception, GetTransactionResponse> {
        val markers = append("transactionId", transactionId)

        val httpRequest = HttpRequest.GET<GetTransactionResponse>("${configuration.transactionPath}/$transactionId")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE).bearerAuth(configuration.apiToken)

        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(GetTransactionResponse::class.java),
            Argument.STRING,
        )

        return try {
            val response = call.firstOrError().blockingGet()
            logger.info(markers, "CafAdapter#getTransaction - success")
            Either.Right(response)
        } catch (err: Exception) {
            logger.error(markers, "CafAdapter#getTransaction", err)
            Either.Left(err)
        }
    }

    override fun retrieveEnrollmentSelfie(livenessId: String): Either<Exception, ByteArray> {
        val markers = append("livenessId", livenessId)
        val logName = "CafAdapter#retrieveEnrollmentSelfie"

        return try {
            // Buscar a transaction usando o livenessId
            getTransaction(livenessId).fold(
                ifLeft = { exception ->
                    logger.error(markers, logName, exception)
                    Either.Left(exception)
                },
                ifRight = { transactionResponse ->
                    val images = transactionResponse.images ?: run {
                        val exception = IllegalStateException("No images found in transaction response")
                        logger.error(markers, logName, exception)
                        return Either.Left(exception)
                    }

                    val selfieUrl = images.selfie ?: run {
                        val exception = IllegalStateException("No selfie URL found in transaction images")
                        logger.error(markers, logName, exception)
                        return Either.Left(exception)
                    }

                    logger.info(markers.andAppend("selfieUrl", selfieUrl), logName)
                    try {
                        val imageBytes = downloadFile(selfieUrl)
                        logger.info(markers.andAppend("imageSize", imageBytes.size), logName)
                        Either.Right(imageBytes)
                    } catch (e: Exception) {
                        logger.error(markers, logName, e)
                        Either.Left(e)
                    }
                },
            )
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            Either.Left(e)
        }
    }

    private fun downloadFile(url: String): ByteArray {
        val markers = append("url", url)
        return try {
            val request = HttpRequest.GET<ByteArray>(url)
            val call = httpClient.retrieve(request, ByteArray::class.java)
            call.blockingFirst().also { logger.info(markers, "CafAdapter#downloadFile") }
        } catch (e: Exception) {
            logger.error("Error downloading file from S3: $url", e)
            throw IllegalStateException("Failed to download file from S3", e)
        }
    }

    override fun validateDocument(livenessId: LivenessId): Either<Exception, DocumentValidationResponse> {
        getTransaction(livenessId.value).fold(
            ifLeft = { exception ->
                logger.error("CafAdapter#validateWithSelfie", exception)
                return Either.Left(exception)
            },
            ifRight = { transactionResponse: GetTransactionResponse ->
                return CafDocumentValidationResponse(
                    selfieMatchesDocument = getSelfieMatchesDocument(transactionResponse),
                    percentage = transactionResponse.sections?.officialData?.confidence ?: 0.0,
                ).right()
            },
        )
    }

    override fun registerFace(personId: String, imageUrl: String): Either<Exception, Unit> {
        val markers = append("personId", personId)
        val logName = "CafAdapter#registerFace"

        val request = FaceRegistrationRequest(
            personId = personId,
            imageUrl = imageUrl,
        )

        val httpRequest = HttpRequest.POST("${configuration.facesPath}", request)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
            .bearerAuth(configuration.apiToken)

        val call = httpClient.retrieve(
            httpRequest,
            Argument.STRING,
            Argument.STRING,
        )

        return try {
            call.firstOrError().blockingGet()
            logger.info(markers, logName)
            Unit.right()
        } catch (err: Exception) {
            logger.error(markers, logName, err)
            Either.Left(err)
        }
    }

    override fun getFaceAuthenticationAttempt(attemptId: String): Either<Exception, FaceAuthenticationAttemptResponse> {
        val markers = append("attemptId", attemptId)
        val logName = "CafAdapter#getFaceAuthenticationAttempt"

        val httpRequest = HttpRequest.GET<FaceAuthenticationAttemptResponse>("${configuration.facesPath}/attempts/$attemptId")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
            .bearerAuth(configuration.apiToken)

        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(FaceAuthenticationAttemptResponse::class.java),
            Argument.STRING,
        )

        return try {
            val response = call.firstOrError().blockingGet()
            logger.info(markers.andAppend("isMatch", response.data.isMatch).andAppend("similarity", response.data.similarity), logName)
            Either.Right(response)
        } catch (err: Exception) {
            logger.error(markers, logName, err)
            Either.Left(err)
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(CafAdapter::class.java)
    }
}

private fun getSelfieMatchesDocument(transactionResponse: GetTransactionResponse): Boolean {
    return when (transactionResponse.sections?.officialData?.probability) {
        OfficialDataProbability.VERY_HIGH,
        OfficialDataProbability.HIGH,
        -> true

        OfficialDataProbability.LOW,
        OfficialDataProbability.VERY_LOW,
        null,
        -> false
    }
}

data class CafDocumentValidationResponse(
    val selfieMatchesDocument: Boolean,
    val percentage: Double,
) : DocumentValidationResponse {
    override fun selfieMatchesDocument() = selfieMatchesDocument

    override fun getPercentage() = percentage
}

data class CreateTransactionTO(
    val templateId: String,
    val files: List<FileData>,
    val attributes: CafAttributes? = null,
    val referenceToken: String,
)

data class CafAttributes(
    val cpf: String? = null,
    val rg: String? = null,
    val name: String? = null,
    val birthDate: String? = null,
    val fatherName: String? = null,
    val motherName: String? = null,
    val uf: String? = null,
    val cnpj: String? = null,
    val cep: String? = null,
    val email: String? = null,
    val phoneNumber: String? = null,
    val plate: String? = null,
)