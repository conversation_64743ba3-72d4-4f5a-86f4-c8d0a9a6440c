package ai.friday.billpayment.adapters.notification

import ai.friday.billpayment.app.AsyncUtils.callAsync
import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.ReceiptFileRepository
import ai.friday.billpayment.app.notification.ByteArrayWithNameAndType
import ai.friday.billpayment.app.payment.ReceiptData
import ai.friday.billpayment.app.payment.ReceiptFilesData
import ai.friday.billpayment.app.payment.receipt.ReceiptTemplateCompilerFinderService
import ai.friday.billpayment.app.utils.TemplateHelper
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.measureTimeInMillis
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import io.micronaut.http.MediaType
import io.via1.communicationcentre.adapters.email.SESAdapterConfiguration
import io.via1.communicationcentre.app.integrations.EmailSenderService
import jakarta.inject.Singleton
import jakarta.mail.internet.MimeUtility
import java.io.ByteArrayInputStream
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
open class BillPaymentEmailSenderService(
    private val emailSender: EmailSenderService,
    private val emailSenderConfiguration: SESAdapterConfiguration,
    private val receiptTemplateCompilerFinderService: ReceiptTemplateCompilerFinderService,
    private val receiptFileRepository: ReceiptFileRepository,
    private val apiFridayAiEmailClient: ApiFridayAiEmailClient,
    private val features: FeatureConfiguration,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    open fun sendEmailAsync(email: EmailToBeSent) {
        callAsync {
            if (features.sendEmailViaApiFridayAi) {
                sendViaApiFridayAi(email)
            } else {
                sendViaCommCenter(email)
            }
        }
    }

    private fun sendViaApiFridayAi(email: EmailToBeSent) {
        try {
            val attachments = email.byteArrayWithNameAndTypes.map { it.toEmailAttachment() }

            when (email) {
                is EmailToBeSentWithRawBody -> {
                    val request = SendRawEmailRequest(
                        senderAddress = email.senderAddress.value,
                        displayName = email.displayName,
                        subject = email.subject,
                        body = email.body,
                        recipient = email.recipient.value,
                        attachments = attachments,
                        mediaType = email.mediaType.toString(),
                    )
                    apiFridayAiEmailClient.sendRawEmail(request)
                }

                is EmailToBeSentWithTemplateBody -> {
                    val request = SendTemplatedEmailRequest(
                        templateName = email.templateName,
                        templateParams = email.templateParams,
                        senderAddress = email.senderAddress.value,
                        displayName = email.displayName,
                        subject = email.subject,
                        recipient = email.recipient.value,
                        attachments = attachments,
                        mediaType = email.mediaType.toString(),
                    )
                    apiFridayAiEmailClient.sendTemplatedEmail(request)
                }
            }
        } catch (e: Exception) {
            logger.error(
                append("emailSubject", email.subject)
                    .andAppend("emailRecipient", email.recipient.value)
                    .andAppend("emailAttachmentNames", email.byteArrayWithNameAndTypes.map { it.fileName })
                    .andAppend("useApiFridayAi", true),
                "BillPaymentEmailSenderService#sendViaApiFridayAi",
                e,
            )
        }
    }

    private fun sendViaCommCenter(email: EmailToBeSent) {
        val mailBody = when (email) {
            is EmailToBeSentWithRawBody -> email.body
            is EmailToBeSentWithTemplateBody -> parseTemplateOrNull(email.templateName, email.templateParams)?.value ?: return
        }

        try {
            val attachments = email.byteArrayWithNameAndTypes.map {
                io.via1.communicationcentre.app.email.Attachment.of(
                    ByteArrayInputStream(it.data),
                    it.mediaType,
                    it.fileName,
                )
            }

            val duration = measureTimeInMillis {
                emailSender.sendRawEmail(
                    email.displayName?.let { buildSource(it, email.senderAddress.value) } ?: email.senderAddress.value,
                    email.subject,
                    mailBody,
                    email.recipient.value,
                    attachments,
                    email.mediaType.toString(),
                )
            }

            logger.info(append("timeTakenInMillis", duration.elapsed).andAppend("useApiFridayAi", false), "sendEmail")
        } catch (e: Exception) {
            logger.error(
                append("emailBody", mailBody.take(1000))
                    .andAppend("emailAttachmentNames", email.byteArrayWithNameAndTypes.map { it.fileName })
                    .andAppend("emailSubject", email.subject)
                    .andAppend("emailRecipient", email.recipient.value)
                    .andAppend("emailMediaType", email.mediaType.toString())
                    .andAppend("useApiFridayAi", false),
                "BillPaymentEmailSenderService#sendViaCommCenter",
                e,
            )
        }
    }

    fun sendTemplatedReceiptEmail(
        templateName: String,
        templateParams: Map<String, String>,
        byteArrayWithNameAndTypes: List<ByteArrayWithNameAndType> = emptyList(),
        subject: String,
        recipient: EmailAddress,
    ) {
        sendEmailAsync(
            EmailToBeSentWithTemplateBody(
                templateName = templateName,
                templateParams = templateParams,
                senderAddress = EmailAddress(emailSenderConfiguration.receiptEmail),
                displayName = emailSenderConfiguration.receiptDisplayName,
                byteArrayWithNameAndTypes = byteArrayWithNameAndTypes,
                subject = subject,
                recipient = recipient,
            ),
        )
    }

    fun sendRawEmail(
        senderAddress: EmailAddress,
        displayName: String?,
        subject: String,
        body: String,
        recipient: EmailAddress,
        attachments: List<ByteArrayWithNameAndType> = emptyList(),
        mediaType: MediaType = MediaType.TEXT_HTML_TYPE,
    ) {
        sendEmailAsync(
            EmailToBeSentWithRawBody(
                senderAddress = senderAddress,
                displayName = displayName,
                subject = subject,
                body = body,
                recipient = recipient,
                byteArrayWithNameAndTypes = attachments,
                mediaType = mediaType,
            ),
        )
    }

    fun sendReceiptEmail(
        receiptData: ReceiptData,
        recipient: EmailAddress,
        wallet: Wallet,
    ) {
        val receiptFilesData = receiptData.generateReceiptFiles(wallet)
        val receiptMailHtml = receiptData.toMail()
        sendReceiptEmail(receiptData, receiptFilesData, receiptMailHtml, recipient)
    }

    fun sendReceiptEmail(
        receiptData: ReceiptData,
        receiptFilesData: ReceiptFilesData,
        receiptMailHtml: CompiledHtml,
        recipient: EmailAddress,
    ) {
        val filename = "${receiptData.dateTime.format(dateFormat)} - ${receiptData.recipientName}.pdf"
        val attachment = ByteArrayWithNameAndType(
            fileName = filename,
            mediaType = MediaType.APPLICATION_PDF,
            data = receiptFilesData.pdfBytes,
        )

        val email = EmailToBeSentWithRawBody(
            senderAddress = EmailAddress(emailSenderConfiguration.receiptEmail),
            displayName = emailSenderConfiguration.receiptDisplayName,
            subject = "Comprovante",
            body = receiptMailHtml.value,
            recipient = recipient,
            byteArrayWithNameAndTypes = listOf(attachment),
            mediaType = MediaType.TEXT_HTML_TYPE,
        )
        sendEmailAsync(email)
    }

    private fun ReceiptData.toMail() =
        receiptTemplateCompilerFinderService.findReceiptTemplateCompiler(this).buildReceiptMailHtml(this) // TODO - adicionar campos do QRCODE

    private fun ReceiptData.generateReceiptFiles(wallet: Wallet): ReceiptFilesData { // TODO - adicionar campos do QRCODE
        val receiptHtml = receiptTemplateCompilerFinderService.findReceiptTemplateCompiler(this).buildReceiptHtml(this, wallet)
        return receiptFileRepository.generateReceiptFiles(this, receiptHtml)
    }

    private fun parseTemplateOrNull(
        templateName: String,
        templateParams: Map<String, String>,
    ): CompiledHtml? {
        return try {
            CompiledHtml(TemplateHelper.applyTemplate(templateName, templateParams))
        } catch (e: Exception) {
            logger.error(append("templateName", templateName).andAppend("templateParams", templateParams), "BillPaymentEmailSenderService#parseTemplateOrNull", e)
            null
        }
    }

    private fun buildSource(
        displayName: String,
        email: String,
    ) = "${MimeUtility.encodeWord(displayName)} <$email>"
}

sealed interface EmailToBeSent {
    val senderAddress: EmailAddress
    val displayName: String?
    val subject: String
    val recipient: EmailAddress
    val byteArrayWithNameAndTypes: List<ByteArrayWithNameAndType>
    val mediaType: MediaType
}

data class EmailToBeSentWithRawBody(
    override val senderAddress: EmailAddress,
    override val displayName: String? = null,
    override val subject: String,
    override val recipient: EmailAddress,
    override val byteArrayWithNameAndTypes: List<ByteArrayWithNameAndType> = emptyList(),
    override val mediaType: MediaType = MediaType.TEXT_HTML_TYPE,
    val body: String,
) : EmailToBeSent

data class EmailToBeSentWithTemplateBody(
    override val senderAddress: EmailAddress,
    override val displayName: String? = null,
    override val subject: String,
    override val recipient: EmailAddress,
    override val byteArrayWithNameAndTypes: List<ByteArrayWithNameAndType> = emptyList(),
    override val mediaType: MediaType = MediaType.TEXT_HTML_TYPE,
    val templateName: String,
    val templateParams: Map<String, String>,
) : EmailToBeSent