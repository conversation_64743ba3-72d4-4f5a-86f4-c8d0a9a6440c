package ai.friday.billpayment.adapters.notification

import ai.friday.billpayment.app.notification.ByteArrayWithNameAndType
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.annotation.ClientFilter
import io.micronaut.http.annotation.FilterMatcher
import io.micronaut.http.annotation.RequestFilter
import io.micronaut.http.client.HttpClient
import io.micronaut.http.client.annotation.Client
import jakarta.inject.Singleton
import java.util.*
import org.slf4j.LoggerFactory

@ConfigurationProperties("email-sender.basic-auth")
interface BasicAuthConfiguration {
    val username: String
    val password: String
}

@FilterMatcher
@MustBeDocumented
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS, AnnotationTarget.VALUE_PARAMETER)
annotation class ApiFridayClientAuth

@ApiFridayClientAuth
@ClientFilter
class ApiFridayAiClientFilter(private val config: BasicAuthConfiguration) {
    @RequestFilter
    fun doFilter(request: MutableHttpRequest<*>) {
        request.basicAuth(config.username, config.password)
    }
}

@Singleton
class ApiFridayAiEmailClient(
    @param:ApiFridayClientAuth
    @param:Client("https://api.friday.ai")
    private val httpClient: HttpClient,
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    fun sendTemplatedEmail(request: SendTemplatedEmailRequest) {
        try {
            val httpRequest = HttpRequest.POST("/email/templated", request)
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Agent", "Friday-BillPayment-Service/1.0")

            httpClient.toBlocking().exchange(httpRequest, String::class.java)
            logger.info("sendTemplatedEmail")
        } catch (e: Exception) {
            logger.error("sendTemplatedEmail", e)
            throw e
        }
    }

    fun sendRawEmail(request: SendRawEmailRequest) {
        try {
            val httpRequest = HttpRequest.POST("/email/raw", request)
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Agent", "Friday-BillPayment-Service/1.0")

            httpClient.toBlocking().exchange(httpRequest, String::class.java)
            logger.info("sendRawEmail")
        } catch (e: Exception) {
            logger.error("sendRawEmail", e)
            throw e
        }
    }
}

data class SendTemplatedEmailRequest(
    val templateName: String,
    val templateParams: Map<String, String>,
    val senderAddress: String,
    val displayName: String?,
    val subject: String,
    val recipient: String,

    val attachments: List<EmailAttachment>,
    val mediaType: String,
)

data class SendRawEmailRequest(
    val senderAddress: String,
    val displayName: String?,
    val subject: String,
    val body: String,
    val recipient: String,
    val attachments: List<EmailAttachment>,
    val mediaType: String,
)

data class EmailAttachment(
    val fileName: String,
    val mediaType: String,
    val data: String, // Base64 encoded
)

fun ByteArrayWithNameAndType.toEmailAttachment(): EmailAttachment {
    return EmailAttachment(
        fileName = fileName,
        mediaType = mediaType.toString(),
        data = Base64.getEncoder().encodeToString(data),
    )
}