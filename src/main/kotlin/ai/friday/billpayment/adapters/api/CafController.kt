package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.caf.CAFJwtPayload
import ai.friday.billpayment.app.caf.CafConfiguration
import ai.friday.billpayment.app.caf.CafService
import ai.friday.billpayment.app.documentscan.CAFDocumentScanPayloadData
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.morning.log.andAppend
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import java.time.LocalDateTime
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(Role.Code.OWNER, Role.Code.GUEST)
@Controller("/caf")
@Version("2")
class CafController(
    private val cafService: CafService,
    private val configuration: CafConfiguration,
    private val accountRegisterRepository: AccountRegisterRepository,
) {
    @Post("/liveness")
    fun createLiveness(
        authentication: Authentication,
        @Body request: CreateLivenessTO,
    ): HttpResponse<*> {
        val markers = append("jwtSize", request.jwt.length)

        return cafService.createLiveness(request.jwt, authentication.toAccountId(), request.referenceToken).fold(
            onFailure = { ex ->
                logger.error(markers, "CafController#createLiveness", ex)
                val errorResponse = ErrorResponseTO(
                    message = "Failed to create CAF liveness: ${ex.message}",
                    code = "CAF_LIVENESS_ERROR",
                )
                HttpResponse.status<ErrorResponseTO>(HttpStatus.BAD_REQUEST).body(errorResponse)
            },
            onSuccess = {
                logger.info(markers, "CafController#createLiveness")
                HttpResponse.ok(ResponseTO(code = "2000", message = "success"))
            },
        )
    }

    @Post("/match")
    fun createMatch(
        authentication: Authentication,
        @Body request: CreateLivenessTO,
    ): HttpResponse<*> {
        val markers = append("jwtSize", request.jwt.length)

        return cafService.createMatch(request.jwt, authentication.toAccountId(), request.referenceToken).fold(
            ifLeft = { ex ->
                logger.error(markers, "CafController#createMatch", ex)
                val errorResponse = ErrorResponseTO(
                    message = "Failed to create CAF match: ${ex.message}",
                    code = "CAF_LIVENESS_ERROR",
                )
                HttpResponse.status<ErrorResponseTO>(HttpStatus.BAD_REQUEST).body(errorResponse)
            },
            ifRight = {
                logger.info(markers, "CafController#createMatch")
                HttpResponse.ok(ResponseTO(code = "2000", message = "success"))
            },
        )
    }

    @Post("/face")
    fun createFace(
        authentication: Authentication,
        @Body request: RegisterFaceTO,
    ): HttpResponse<*> {
        val markers = append("imageUrl", request.imageUrl)

        return cafService.registerFace(authentication.toAccountId(), request.imageUrl).fold(
            ifLeft = { ex ->
                logger.error(markers, "CafController#createFace", ex)
                val errorResponse = ErrorResponseTO(
                    message = "Failed to create CAF face: $ex",
                    code = "CAF_FACE_ERROR",
                )
                HttpResponse.status<ErrorResponseTO>(HttpStatus.BAD_REQUEST).body(errorResponse)
            },
            ifRight = {
                logger.info(markers, "CafController#createFace")
                HttpResponse.ok(ResponseTO(code = "2000", message = "success"))
            },
        )
    }

    @Get("/match/{livenessId}")
    fun getMatch(
        @PathVariable livenessId: String,
        authentication: Authentication,
    ): HttpResponse<*> {
        val markers = append("livenessId", livenessId)
        val accountRegister = accountRegisterRepository.findByAccountId(authentication.toAccountId())

        if (accountRegister.livenessId == null) {
            logger.error(markers, "CafController#getMatch - No livenessId found for account")
            val errorResponse = ErrorResponseTO(
                message = "No livenessId found for account",
                code = "CAF_NO_LIVENESS_ID",
            )
            return HttpResponse.status<ErrorResponseTO>(HttpStatus.BAD_REQUEST).body(errorResponse)
        }

        return cafService.verifyMatch(accountRegister.livenessId).fold(
            ifLeft = { error ->
                logger.error(markers, "CafController#getMatch", error)
                val errorResponse = ErrorResponseTO(
                    message = "Failed to retrieve CAF match: $error",
                    code = "CAF_GET_MATCH_ERROR",
                )
                HttpResponse.status<ErrorResponseTO>(HttpStatus.BAD_REQUEST).body(errorResponse)
            },
            ifRight = { matchResult ->
                logger.info(markers.andAppend("status", matchResult), "CafController#getMatch")
                HttpResponse.ok(matchResult)
            },
        )
    }

    @Post("/document-detector")
    fun createDocument(
        authentication: Authentication,
        @Body request: CreateDocumentDetectorTO,
    ): HttpResponse<*> {
        val markers = append("jwtSize", request.jwt.length)

        return cafService.upsertDocumentScanId(
            CAFDocumentScanPayloadData(accountId = authentication.toAccountId(), jwt = request.jwt, referenceToken = request.referenceToken),
        ).fold(
            ifLeft = { ex ->
                logger.error(markers, "CafController#createDocument", ex)
                val errorResponse = ErrorResponseTO(
                    message = "Failed to create CAF document detector: ${ex::class.java}",
                    code = "4000",
                )
                HttpResponse.badRequest(errorResponse)
            },
            ifRight = {
                logger.info(markers, "CafController#createDocument")
                HttpResponse.ok(ResponseTO(code = "2000", message = "success"))
            },
        )
    }

    @Post("/transactions")
    fun createTransaction(
        @Body request: CreateTransactionRequestTO,
    ): HttpResponse<*> {
        val markers = append("templateId", request.templateId)

        return cafService.createTransaction(templateId = request.templateId, files = request.files.toListFileData(), "", null, null, null).fold(
            ifLeft = { ex ->
                logger.error(markers, "CafController#createTransaction", ex)
                val errorResponse = ErrorResponseTO(
                    message = "Failed to create CAF transaction: ${ex.message}",
                    code = "CAF_CREATE_ERROR",
                )
                HttpResponse.status<ErrorResponseTO>(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse)
            },
            ifRight = { response ->
                val responseTO = CreateTransactionResponseTO(
                    requestId = response.requestId,
                    id = response.id,
                    status = response.status,
                    success = true,
                )
                logger.info(
                    markers.andAppend("transactionId", response.id),
                    "CafController#createTransaction",
                )
                HttpResponse.status<CreateTransactionResponseTO>(HttpStatus.CREATED).body(responseTO)
            },
        )
    }

    @Get("/transactions")
    fun listTransactions(): HttpResponse<*> {
        logger.info("CafController#listTransactions")

        return cafService.listTransactions().fold(
            ifLeft = { ex ->
                logger.error("CafController#listTransactions", ex)
                val errorResponse = ErrorResponseTO(
                    message = "Failed to list CAF transactions: ${ex.message}",
                    code = "CAF_LIST_ERROR",
                )
                HttpResponse.status<ErrorResponseTO>(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse)
            },
            ifRight = { response ->
                val responseTO = ListTransactionsResponseTO(
                    requestId = response.requestId,
                    items = response.items.map { item ->
                        TransactionItemTO(
                            id = item.id,
                            status = item.status,
                            createdAt = item.createdAt,
                            data = item.data?.let { data ->
                                TransactionDataTO(
                                    cpf = data.cpf,
                                    birthDate = data.birthDate,
                                    name = data.name,
                                )
                            },
                        )
                    },
                    totalItems = response.totalItems,
                    success = true,
                )
                logger.info(
                    append("totalItems", response.totalItems),
                    "CafController#listTransactions",
                )
                HttpResponse.ok(responseTO)
            },
        )
    }

    @Get("/transactions/{transactionId}")
    fun getTransaction(@PathVariable transactionId: String): HttpResponse<*> {
        val markers = append("transactionId", transactionId)

        return cafService.getTransaction(transactionId).fold(
            ifLeft = { ex ->
                logger.error(markers, "CafController#getTransaction", ex)
                val errorResponse = ErrorResponseTO(
                    message = "Failed to retrieve CAF transaction: ${ex.message}",
                    code = "CAF_GET_ERROR",
                )
                HttpResponse.status<ErrorResponseTO>(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse)
            },
            ifRight = { response ->
                val responseTO = GetTransactionResponseTO(
                    requestId = response.requestId,
                    id = response.id,
                    status = response.status,
                    success = true,
                )
                logger.info(
                    markers.andAppend("status", response.status),
                    "CafController#getTransaction",
                )
                HttpResponse.ok(responseTO)
            },
        )
    }

    @Get("/token")
    fun createJwtToken(): HttpResponse<*> {
        val logName = "CafController#createJwtToken"

        return try {
            val now = System.currentTimeMillis() / 1000
            val expirationTime = now + 10 * 60 // 10 minutos

            val payload = CAFJwtPayload(
                iss = configuration.clientId,
                exp = expirationTime,
            )

            val token = cafService.generateJwtToken(payload, configuration.jwtSecret)

            val responseTO = JwtTokenResponseTO(
                jwt = token,
            )

            logger.info(logName)
            HttpResponse.ok(responseTO)
        } catch (ex: Exception) {
            logger.error(logName, ex)
            val errorResponse = ErrorResponseTO(
                message = "Failed to create JWT token: ${ex.message}",
                code = "JWT_CREATION_ERROR",
            )
            HttpResponse.status<ErrorResponseTO>(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse)
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(CafController::class.java)
    }
}

data class CreateTransactionRequestTO(
    val templateId: String,
    val files: List<FileDataTO>,
)

data class FileDataTO(
    val data: String,
    val type: String,
)

data class CreateTransactionResponseTO(
    val requestId: String,
    val id: String?,
    val status: String?,
    val success: Boolean = true,
    val message: String? = null,
)

data class ListTransactionsResponseTO(
    val requestId: String,
    val items: List<TransactionItemTO>,
    val totalItems: Int,
    val success: Boolean = true,
    val message: String? = null,
)

data class TransactionItemTO(
    val id: String,
    val status: String,
    val createdAt: LocalDateTime,
    val data: TransactionDataTO?,
)

data class TransactionDataTO(
    val cpf: String?,
    val birthDate: String?,
    val name: String?,
)

data class GetTransactionResponseTO(
    val requestId: String,
    val id: String,
    val status: String,
    val success: Boolean = true,
    val message: String? = null,
)

data class ErrorResponseTO(
    val success: Boolean = false,
    val message: String,
    val code: String? = null,
)

data class CreateLivenessTO(
    val jwt: String,
    val referenceToken: String,
)

data class RegisterFaceTO(
    val imageUrl: String,
)

data class CreateDocumentDetectorTO(
    val jwt: String,
    val referenceToken: String,
)

data class JwtTokenResponseTO(
    val jwt: String,
)