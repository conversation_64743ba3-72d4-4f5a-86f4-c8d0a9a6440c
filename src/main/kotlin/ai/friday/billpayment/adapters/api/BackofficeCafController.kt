package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.caf.CafService
import ai.friday.billpayment.app.caf.FileData
import ai.friday.morning.log.andAppend
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Controller("/backoffice/caf")
@Secured("ADMIN")
class BackofficeCafController(
    private val cafService: CafService,
) {
    @Post("/transactions")
    fun createTransaction(
        @Body request: CreateTransactionRequestTO,
    ): HttpResponse<*> {
        val markers = append("templateId", request.templateId)

        return cafService.createTransaction(templateId = request.templateId, files = request.files.toListFileData(), "", null, null, null).fold(
            ifLeft = { ex ->
                logger.error(markers, "BackofficeCafController#createTransaction", ex)
                val errorResponse = ErrorResponseTO(
                    message = "Failed to create CAF transaction: ${ex.message}",
                    code = "CAF_CREATE_ERROR",
                )
                HttpResponse.status<ErrorResponseTO>(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse)
            },
            ifRight = { response ->
                val responseTO = CreateTransactionResponseTO(
                    requestId = response.requestId,
                    id = response.id,
                    status = response.status,
                    success = true,
                )
                logger.info(
                    markers.andAppend("transactionId", response.id),
                    "BackofficeCafController#createTransaction",
                )
                HttpResponse.status<CreateTransactionResponseTO>(HttpStatus.CREATED).body(responseTO)
            },
        )
    }

    @Get("/transactions")
    fun listTransactions(): HttpResponse<*> {
        logger.info("BackofficeCafController#listTransactions")

        return cafService.listTransactions().fold(
            ifLeft = { ex ->
                logger.error("BackofficeCafController#listTransactions", ex)
                val errorResponse = ErrorResponseTO(
                    message = "Failed to list CAF transactions: ${ex.message}",
                    code = "CAF_LIST_ERROR",
                )
                HttpResponse.status<ErrorResponseTO>(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse)
            },
            ifRight = { response ->
                val responseTO = ListTransactionsResponseTO(
                    requestId = response.requestId,
                    items = response.items.map { item ->
                        TransactionItemTO(
                            id = item.id,
                            status = item.status,
                            createdAt = item.createdAt,
                            data = item.data?.let { data ->
                                TransactionDataTO(
                                    cpf = data.cpf,
                                    birthDate = data.birthDate,
                                    name = data.name,
                                )
                            },
                        )
                    },
                    totalItems = response.totalItems,
                    success = true,
                )
                logger.info(
                    append("totalItems", response.totalItems),
                    "BackofficeCafController#listTransactions",
                )
                HttpResponse.ok(responseTO)
            },
        )
    }

    @Get("/transactions/{transactionId}")
    fun getTransaction(@PathVariable transactionId: String): HttpResponse<*> {
        val markers = append("transactionId", transactionId)

        return cafService.getTransaction(transactionId).fold(
            ifLeft = { ex ->
                logger.error(markers, "BackofficeCafController#getTransaction", ex)
                val errorResponse = ErrorResponseTO(
                    message = "Failed to retrieve CAF transaction: ${ex.message}",
                    code = "CAF_GET_ERROR",
                )
                HttpResponse.status<ErrorResponseTO>(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse)
            },
            ifRight = { response ->
                val responseTO = GetTransactionResponseTO(
                    requestId = response.requestId,
                    id = response.id,
                    status = response.status,
                    success = true,
                )
                logger.info(
                    markers.andAppend("status", response.status),
                    "BackofficeCafController#getTransaction",
                )
                HttpResponse.ok(responseTO)
            },
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(BackofficeCafController::class.java)
    }
}

fun List<FileDataTO>.toListFileData(): List<FileData> =
    this.map {
        FileData(
            data = it.data,
            type = it.type,
        )
    }