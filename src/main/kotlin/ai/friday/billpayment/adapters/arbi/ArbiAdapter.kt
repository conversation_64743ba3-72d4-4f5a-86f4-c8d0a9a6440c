package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.adapters.circuitbreaker.CircuitBreakerHoF
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.FinancialInstitutionGlobalData
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.ZipCode
import ai.friday.billpayment.app.account.hasDeveloperEarlyAccess
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.BankStatement
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.DefaultBankStatementItem
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.CreateBoletoRequest
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.dda.CallDDAAgregadoException
import ai.friday.billpayment.app.dda.DDAItem
import ai.friday.billpayment.app.dda.DDAProvider
import ai.friday.billpayment.app.firstNotNullAndNotEmpty
import ai.friday.billpayment.app.getOrFalse
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.billpayment.app.integrations.BillValidationService
import ai.friday.billpayment.app.integrations.DDABatchResult
import ai.friday.billpayment.app.integrations.DDAProviderService
import ai.friday.billpayment.app.integrations.DDAQueryBatchAddResult
import ai.friday.billpayment.app.integrations.DDAQueryBatchRemoveResult
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.ZipCodeService
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.BillRegisterData
import ai.friday.billpayment.app.payment.BillValidationResponse
import ai.friday.billpayment.app.payment.BoletoSettlementException
import ai.friday.billpayment.app.payment.DiscountData
import ai.friday.billpayment.app.payment.DiscountType
import ai.friday.billpayment.app.payment.DivergentPayment
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.payment.FineData
import ai.friday.billpayment.app.payment.FineType
import ai.friday.billpayment.app.payment.InterestData
import ai.friday.billpayment.app.payment.InterestType
import ai.friday.billpayment.app.payment.PartialPayment
import ai.friday.billpayment.app.payment.PartialPaymentAmountType
import ai.friday.billpayment.app.payment.RecipientChain
import ai.friday.billpayment.app.payment.getRecipientByPayer
import ai.friday.billpayment.app.payment.isCPF
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.timeFormat
import ai.friday.morning.log.andAppend
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonValue
import com.fasterxml.jackson.core.json.JsonReadFeature
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.cache.annotation.CacheConfig
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.HttpVersion
import io.micronaut.http.MediaType
import io.micronaut.http.client.HttpClientConfiguration
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientException
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.uri.UriBuilder
import io.micronaut.runtime.ApplicationConfiguration
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.math.BigDecimal
import java.math.RoundingMode
import java.text.DecimalFormat
import java.text.NumberFormat
import java.time.DayOfWeek
import java.time.Duration
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.math.roundToLong
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
class ArbiHttpConfiguration(
    applicationConfiguration: ApplicationConfiguration,
    val configuration: HttpClientConfiguration,
) : HttpClientConfiguration(applicationConfiguration) {
    init {
        setReadTimeout(Duration.ofMinutes(1))
        httpVersion = HttpVersion.HTTP_1_1
    }

    override fun getConnectionPoolConfiguration(): ConnectionPoolConfiguration =
        configuration.connectionPoolConfiguration

    override fun getMaxContentLength(): Int = ********
}

private const val ARBI_BANK_NO = "213"
private const val ARBI_ROUTING_NO = "00019"
private val arbiDateFormat: DateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy")

private enum class MaintenanceType(val code: String) {
    Include("I"), Exclude("E")
}

@Singleton
@CacheConfig("arbi")
@Requires(env = [FRIDAY_ENV, MODATTA_ENV, ME_POUPE_ENV])
open class ArbiAdapter(
    @param:Client(
        id = "arbi",
    ) private val httpClient: RxHttpClient,
    private val configuration: ArbiConfiguration,
    private val authenticationManager: NewArbiAuthenticationManager,
    @Property(name = "features.fallbackCheckTransfer", defaultValue = "false") private val fallbackCheckTransfer: Boolean,
) : BankAccountService, DDAProviderService, BillValidationService, ZipCodeService {

    val mapper = jacksonObjectMapper()

    @field:Property(name = "integrations.arbi.paymentTimeLimit")
    lateinit var paymentTimeLimit: String

    @CircuitBreakerHoF(name = "GetBalance")
    override fun getBalance(accountNo: String): Long {
        val logName = "ArbiBalance"
        val token = authenticationManager.getToken()
        val request = CheckingRequestTO(
            contaCorrente = ContaCorrenteTO(
                inscricaoParceiro = configuration.inscricao,
                tokenUsuario = configuration.userToken,
                idModulo = "1",
                idTransacao = "3",
                dataAgendamento = getScheduleDate(),
                contaOrigem = accountNo,
            ),
        )
        val marker = append("requestBody", request)
        val httpRequest = HttpRequest.POST(configuration.checkingV2Path, request)
            .header("client_id", configuration.clientId)
            .header("access_token", token)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        marker.andAppend("request", httpRequest.toString())
        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(ArbiResponseTO::class.java),
            Argument.STRING,
        )
        try {
            val response = call.firstOrError().blockingGet()
            marker.andAppend("response", response)

            val balance = convertToLongOrException(response.resultado)
            if (balance < 0) {
                marker.andAppend("negativeBalance", balance)
                val ex = ArbiInvalidBalanceException("Arbi Balance can't be negative: $balance")
                logger.warn(marker, logName, ex)
                throw ex
            }
            logger.info(marker, logName)
            return balance
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)

            val responseBody = e.response.getBody(String::class.java).get()

            val invalidAccount = e.status == HttpStatus.UNPROCESSABLE_ENTITY && responseBody
                .contains("Conta origem não cadastrado e/ou invalido.")

            val accountNotAllowed = e.status == HttpStatus.FORBIDDEN && responseBody
                .contains("Usuario/Modulo/transacao/Conta sem permissão.")

            val response = e.response.getBody(String::class.java).get()
            if (invalidAccount) {
                logger.warn(
                    marker.andAppend("response", response),
                    logName,
                    e,
                )
                throw ArbiInvalidAccountException()
            } else if (accountNotAllowed) {
                logger.warn(
                    marker.andAppend("response", response),
                    logName,
                    e,
                )
                throw ArbiAccountMissingPermissionsException()
            } else {
                logger.error(
                    marker.andAppend("response", response),
                    logName,
                    e,
                )
                throw ArbiAdapterException()
            }
        } catch (e: ArbiInvalidBalanceException) {
            throw e
        } catch (e: HttpClientException) {
            logger.error(marker, logName, e)
            throw ArbiAdapterException()
        } catch (e: Exception) {
            logger.error(marker.andAppend("ACTION", "VERIFY"), logName, e)
            throw ArbiAdapterException()
        }
    }

    @CircuitBreakerHoF(name = "GetStatement")
    override fun getStatement(
        accountNumber: AccountNumber,
        document: String,
        initialDate: ZonedDateTime,
        endDate: ZonedDateTime,
    ): BankStatement {
        val token = authenticationManager.getToken()
        val request = CheckingRequestTO(
            contaCorrente = ContaCorrenteTO(
                inscricaoParceiro = configuration.inscricao,
                tokenUsuario = configuration.userToken,
                idModulo = "1",
                idTransacao = "4",
                contaOrigem = accountNumber.fullAccountNumber,
                dataInicial = initialDate.format(DateTimeFormatter.ISO_DATE),
                dataAgendamento = getScheduleDate(),
                dataFinal = endDate.format(DateTimeFormatter.ISO_DATE),
            ),
        )
        val marker = append("request", request)
        val httpRequest = HttpRequest.POST(configuration.getStatementV2Path, request)
            .header("client_id", configuration.clientId)
            .header("access_token", token)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        val call = httpClient.exchange(
            httpRequest,
            Argument.listOf(ArbiResponseTO::class.java),
            Argument.STRING,
        )
        try {
            val response = call.firstOrError().blockingGet()
            val optResponseBody = response.getBody(Argument.listOf(ArbiResponseTO::class.java))
            marker.andAppend("response", optResponseBody.orElse(emptyList()))

            if (optResponseBody.isEmpty || (
                response.status == HttpStatus.OK && optResponseBody.get()
                    .first().resultado == "Sem movimentos para o período informado."
                )
            ) {
                logger.info(
                    marker,
                    "ArbiStatement",
                )
                return BankStatement(emptyList())
            }

            @Suppress("UNCHECKED_CAST")
            val mappedResponse: List<Map<String, String>> = optResponseBody.get()
                .map { jacksonObjectMapper().readValue(it.resultado, Map::class.java) } as List<Map<String, String>>
            logger.info(
                marker.andAppend("response", mappedResponse),
                "ArbiStatement",
            )
            return BankStatement(mappedResponse.map { convertToBankStatement(it, document) })
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)
            logger.error(
                marker.and<LogstashMarker>(
                    append(
                        "response",
                        e.response.getBody(String::class.java).orElse(""),
                    ),
                )
                    .and<LogstashMarker>(append("status", e.status)),
                "ArbiStatement",
                e,
            )
            throw ArbiBankAccountException()
        } catch (e: HttpClientException) {
            logger.error(marker, "ArbiStatement", e)
            throw ArbiAdapterException()
        } catch (e: Exception) {
            logger.error(marker.andAppend("ACTION", "VERIFY"), "ArbiStatement", e)
            throw ArbiBankAccountException()
        }
    }

    @CircuitBreakerHoF(name = "Transfer")
    override fun transfer(
        originAccountNo: String,
        targetAccountNo: String,
        amount: Long,
        operationId: BankOperationId,
    ): BankTransfer {
        val request = CheckingRequestTO(
            contaCorrente = ContaCorrenteTO(
                inscricaoParceiro = configuration.inscricao,
                idRequisicao = IdRequisicao(operationId.value),
                tokenUsuario = configuration.userToken,
                idModulo = "1",
                idTransacao = "1",
                contaOrigem = originAccountNo,
                bancoDestino = ARBI_BANK_NO,
                agenciaDestino = ARBI_ROUTING_NO,
                contaDestino = targetAccountNo,
                tipoContaCreditada = "CC",
                dataAgendamento = getScheduleDate(),
                valor = convertToString(amount),
            ),
        )
        val marker = append("request", request)

        try {
            val token = authenticationManager.getToken()
            val httpRequest = HttpRequest.POST(configuration.checkingV2Path, request)
                .header("client_id", configuration.clientId)
                .header("access_token", token)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON_TYPE)
            val call = httpClient.retrieve(
                httpRequest,
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.listOf(ArbiResponseTO::class.java),
            )

            val response = call.firstOrError().blockingGet()

            val (nroMovDeb, nroMovCred) = jacksonObjectMapper().readerFor(ArbiTransferResponseTO::class.java)
                .readValue<ArbiTransferResponseTO>(response.first().resultado)

            logger.info(marker.and(append("response", response)), "ArbiTransfer")
            return BankTransfer(
                operationId = operationId,
                status = BankOperationStatus.SUCCESS,
                amount = amount,
                gateway = FinancialServiceGateway.ARBI,
                debitOperationNumber = nroMovDeb,
                creditOperationNumber = nroMovCred,
            )
        } catch (e: HttpClientResponseException) {
            val bodyStr = e.response.getBody(String::class.java).orElse("")
            marker.andAppend("status", e.status).andAppend("bodyStr", bodyStr)
            if (isUnauthorizedBankTransfer(e)) {
                return BankTransfer(
                    operationId = operationId,
                    status = BankOperationStatus.ERROR,
                    amount = amount,
                    errorDescription = "Arbi auth error",
                    gateway = FinancialServiceGateway.ARBI,
                )
            }

            val successRange = 200..299
            if (e.status.code in successRange) {
                return unknownTransferResponse(marker, e, operationId, amount)
            }

            if (bodyStr.contains("upstream connect error or disconnect/reset before headers.")) {
                return unknownTransferResponse(marker, e, operationId, amount)
            }

            val optResponse = readArbiExceptionResponse(marker, e)
            if (optResponse.isPresent) {
                val response = optResponse.get()
                return when {
                    response.any { item -> item.descricaoStatus == "Saldo disponivel insuficiente." } -> {
                        logger.warn(marker.and(append("response", response)), "ArbiTransfer")
                        BankTransfer(
                            operationId = operationId,
                            status = BankOperationStatus.INSUFFICIENT_FUNDS,
                            amount = amount,
                            errorDescription = "Insufficient funds",
                            gateway = FinancialServiceGateway.ARBI,
                        )
                    }

                    response.any { item -> item.resultado == "1 - Timeout alcancado 0" } -> {
                        logger.error(marker.and(append("response", response)), "ArbiTransfer")
                        BankTransfer(
                            operationId = operationId,
                            status = BankOperationStatus.TIMEOUT,
                            amount = amount,
                            gateway = FinancialServiceGateway.ARBI,
                        )
                    }

                    else -> {
                        logger.error(marker.and(append("response", response)), "ArbiTransfer")
                        BankTransfer(
                            operationId = operationId,
                            status = BankOperationStatus.ERROR,
                            amount = amount,
                            errorDescription = response.joinToString { "${it.descricaoStatus} - ${it.resultado}" },
                            gateway = FinancialServiceGateway.ARBI,
                        )
                    }
                }
            }
            marker.andAppend("optResponse", optResponse.orElse(null))
            return unknownTransferResponse(marker, e, operationId, amount)
        } catch (e: Exception) {
            return unknownTransferResponse(marker, e, operationId, amount)
        }
    }

    @CircuitBreakerHoF(name = "CheckTransferStatus")
    override fun checkTransferStatus(
        idRequisicaoParceiroOriginal: String,
        startDate: LocalDate,
        endDate: LocalDate,
        originAccountNo: String,
    ): Boolean {
        val shouldForceFallback = fallbackCheckTransfer && originAccountNo.hasDeveloperEarlyAccess()
        val markers = append("idRequisicaoParceiroOriginal", idRequisicaoParceiroOriginal)
            .andAppend("shouldForceFallback", shouldForceFallback)
            .andAppend("fallbackCheckTransfer", fallbackCheckTransfer)

        try {
            return if (!shouldForceFallback && defaultCheckTransferStatus(
                    idRequisicaoParceiroOriginal = idRequisicaoParceiroOriginal,
                    startDate = startDate,
                    endDate = endDate,
                    originAccountNo = originAccountNo,
                    markers,
                )
            ) {
                markers.andAppend("fallback", false)
                return true
            } else {
                if (fallbackCheckTransfer) {
                    markers.andAppend("fallback", true)
                    fallbackCheckTransferStatus(
                        idRequisicaoParceiroOriginal = idRequisicaoParceiroOriginal,
                        startDate = startDate,
                        endDate = endDate,
                        originAccountNo = originAccountNo,
                        markers,
                    )
                } else {
                    false
                }.also {
                    markers.andAppend("result", it)
                    logger.info(markers, "ArbiCheckTransfer")
                }
            }
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)
            logger.error(
                markers.andAppend("response", e.response.getBody(String::class.java).orElse(""))
                    .andAppend("status", e.status),
                "ArbiCheckTransfer",
            )
            throw ArbiCheckTransferException()
        } catch (e: HttpClientException) {
            logger.error(markers, "ArbiCheckTransfer", e)
            throw ArbiAdapterException()
        } catch (e: Exception) {
            logger.error(markers.andAppend("ACTION", "VERIFY"), "ArbiCheckTransfer", e)
            throw ArbiCheckTransferException()
        }
    }

    private fun defaultCheckTransferStatus(
        idRequisicaoParceiroOriginal: String,
        startDate: LocalDate,
        endDate: LocalDate,
        originAccountNo: String,
        markers: LogstashMarker,
    ): Boolean {
        val request = CheckingRequestTO(
            contaCorrente = ContaCorrenteTO(
                inscricaoParceiro = configuration.inscricao,
                tokenUsuario = configuration.userToken,
                idModulo = "1",
                idTransacao = "5",
                contaOrigem = originAccountNo,
                dataInicial = startDate.format(DateTimeFormatter.ISO_DATE),
                dataAgendamento = getScheduleDate(),
                dataFinal = endDate.format(DateTimeFormatter.ISO_DATE),
            ),
        )

        markers.andAppend("request", request)

        val httpRequest = HttpRequest.POST(configuration.checkingV2Path, request)
            .header("client_id", configuration.clientId)
            .header("access_token", authenticationManager.getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        val httpResponse = httpClient.exchange(
            httpRequest,
            Argument.listOf(ArbiResponseTO::class.java),
            Argument.listOf(String::class.java),
        )
            .firstOrError()
            .blockingGet()

        markers.andAppend("response", httpResponse)

        val responseBody = httpResponse.getBody(Argument.listOf(ArbiResponseTO::class.java))

        if (
            responseBody.isEmpty ||
            (httpResponse.status == HttpStatus.OK && responseBody.get().first().resultado == "Sem requisicões/liquidações para o período informado.")
        ) {
            return false
        }

        return responseBody.get()
            .mapNotNull {
                if (it.resultado.isNullOrEmpty()) {
                    null
                } else {
                    jacksonObjectMapper().readerFor(Map::class.java).readValue<Map<String, String>>(it.resultado)
                }
            }
            .also {
                if (it.isEmpty()) {
                    logger.warn(markers, "ArbiCheckTransfer#responseWithEmptyResult")
                }
            }
            .any { it["idrequisicaoparceirooriginal"] == idRequisicaoParceiroOriginal }
    }

    private fun fallbackCheckTransferStatus(
        idRequisicaoParceiroOriginal: String,
        startDate: LocalDate,
        endDate: LocalDate,
        originAccountNo: String,
        markers: LogstashMarker,
    ): Boolean {
        val logName = "ArbiStatement#fallbackCheckTransferStatus"

        val request = CheckingRequestTO(
            contaCorrente = ContaCorrenteTO(
                inscricaoParceiro = configuration.inscricao,
                tokenUsuario = configuration.userToken,
                idModulo = "1",
                idTransacao = "4",
                contaOrigem = originAccountNo,
                dataInicial = startDate.format(DateTimeFormatter.ISO_DATE),
                dataAgendamento = getScheduleDate(),
                dataFinal = endDate.format(DateTimeFormatter.ISO_DATE),
            ),
        )

        logger.info(markers.andAppend("request", request), logName)

        val httpRequest = HttpRequest.POST(configuration.getStatementV2Path, request)
            .header("client_id", configuration.clientId)
            .header("access_token", authenticationManager.getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        val response = httpClient.exchange(httpRequest, Argument.listOf(ArbiResponseTO::class.java), Argument.STRING)
            .firstOrError()
            .blockingGet()

        val body = response.getBody(Argument.listOf(ArbiResponseTO::class.java))

        markers.andAppend("response", body.orElse(emptyList()))

        if (body.isEmpty ||
            (response.status == HttpStatus.OK && body.get().first().resultado == "Sem movimentos para o período informado.")
        ) {
            return false
        }

        @Suppress("UNCHECKED_CAST")
        val entries: List<Map<String, String>> = body.get()
            .map { jacksonObjectMapper().readValue(it.resultado, Map::class.java) } as List<Map<String, String>>

        val debitTransfers = entries.filter {
            val type = getBankStatementType(code = it["codhist"]!!, sameOwnership = false) // NOTE: sameOwnership não importa, pois estamos buscando somente TEFs
            (type == BankStatementItemType.TRANSFERENCIA_CC && !it["natureza"].equals("C"))
        }

        markers.andAppend("response", null) // NOTE: pode ser muito grande
            .andAppend("debitTransfers", debitTransfers)

        return debitTransfers.any {
            val finalidade = it.getOrDefault("finalidade", null) ?: return@any false

            return@any when (val parsed = TefFinalidadeParseResult.create(finalidade)) {
                is TefFinalidadeParseResult.Valid -> {
                    parsed.idRequisicaoParceiroOriginal
                        ?.equals(idRequisicaoParceiroOriginal, ignoreCase = true)
                        ?: false
                }

                TefFinalidadeParseResult.Invalid -> {
                    logger.error(append("finalidade", finalidade), logName)
                    false
                }
            }
        }
    }

    private fun checkUnauthorized(e: HttpClientResponseException) {
        if (e.status == HttpStatus.UNAUTHORIZED) {
            logger.warn(append("error_message", "Token is expired"), "ArbiAdapter")
            authenticationManager.cleanTokens()
            throw ArbiLoginException()
        }
    }

    fun pay(barCode: BarCode, amount: Long): String? {
        val token = authenticationManager.getToken()

        // FIXME o request é o mesmo mas o nome ficou esquisitos
        val billValidateRequestTO = BillValidateRequestTO(
            pagamentos = PagamentosTO(
                inscricaoParceiro = configuration.inscricao,
                tokenUsuario = configuration.userToken,
                codigoBarras = barCode.number,
                contaOrigem = configuration.contaLiquidacao,
                valorPagar = convertToString(amount),
                dataPgto = getFirstWorkingPaymentDate(),
                idTransacao = "2",
            ),
        )

        val marker = append("barCode", barCode)
            .andAppend("amount", amount)
            .andAppend("request", billValidateRequestTO)

        val httpRequest = HttpRequest.POST(configuration.paymentV2Path, billValidateRequestTO)
            .header("client_id", configuration.clientId)
            .header("access_token", token)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        val call = httpClient.exchange(
            httpRequest,
            Argument.of(String::class.java),
            Argument.listOf(String::class.java),
        )

        try {
            val httpResponse = call.firstOrError().blockingGet()

            logger.info(marker.andAppend("responseBody", httpResponse.body()), "ArbiAdapter#pay")
            return httpResponse.body()
        } catch (e: HttpClientResponseException) {
            val body = e.response.getBody(String::class.java)
            logger.error(
                marker.andAppend("responseBody", body)
                    .andAppend("responseStatus", e.status),
                "ArbiAdapter#pay",
            )
            return body.orElseGet { "" }
        } catch (e: Exception) {
            logger.error(marker, "ArbiAdapter#pay", e)
            return e.message
        }
    }

    @CircuitBreakerHoF(name = "Validate")
    override fun validate(request: CreateBoletoRequest): BillValidationResponse {
        return fetchValidate(request.barcode)
    }

    @CircuitBreakerHoF(name = "Validate")
    override fun validate(bill: Bill): BillValidationResponse {
        return fetchValidate(bill.barcode!!)
    }

    @CircuitBreakerHoF(name = "Validate")
    override fun validate(barCode: BarCode): BillValidationResponse {
        return fetchValidate(barCode)
    }

    @CircuitBreakerHoF(name = "GetBills")
    override fun getBills(dueDate: LocalDate, document: String): List<DDAItem> {
        return getBills(
            DDATO(
                dda = DDA(
                    inscricaoParceiro = configuration.getInscricaoParceiro(),
                    tokenUsuario = configuration.userToken,
                    contaTitular = configuration.contaTitular,
                    inscricao = document,
                    tipoPessoa = if (isCPF(document)) "F" else "J",
                    tipoRetornoTitulos = "T",
                    dataVctoTitulo = dueDate.format(dateFormat),
                ),
            ),
        )
    }

    override fun getBillsWithoutMicronautParser(dueDate: LocalDate, document: String): Boolean {
        val markers = Markers.append("dueDate", dueDate)
            .andAppend("document", document)

        try {
            val dda = DDATO(
                dda = DDA(
                    inscricaoParceiro = configuration.inscricao,
                    tokenUsuario = configuration.userToken,
                    contaTitular = configuration.contaTitular,
                    inscricao = document,
                    tipoPessoa = if (isCPF(document)) "F" else "J",
                    tipoRetornoTitulos = "T",
                    dataVctoTitulo = dueDate.format(dateFormat),
                ),
            )
            markers.andAppend("dda", dda)

            val token = authenticationManager.getToken()

            val command = listOf(
                "curl",
                "--trace",
                "-",
                "-XPOST",
                "${configuration.newHost}${configuration.ddaV2Path}",
                "--header",
                "Content-Type: application/json",
                "--header",
                "Accept: application/json",
                "--header",
                "client_id: ${configuration.clientId}",
                "--header",
                "access_token: $token",

                "--data",
                """${getObjectMapper().writeValueAsString(dda)}""",
            )
            markers.andAppend("command", command)

            val process = ProcessBuilder(command).start()

            val terminated = process.waitFor(1, TimeUnit.MINUTES)
            markers.andAppend("terminated", terminated)

            val output = process.inputStream.reader(Charsets.UTF_8).use {
                it.readText()
            }
            markers.andAppend("output", output)

            val error = process.errorStream.reader(Charsets.UTF_8).use {
                it.readText()
            }
            markers.andAppend("error", error)

            logger.info(markers, "ArbiAdapter#getBillsWithoutMicronautParser")

            return true
        } catch (e: Exception) {
            logger.error(markers, "ArbiAdapter#getBillsWithoutMicronautParser", e)

            return false
        }
    }

    @CircuitBreakerHoF(name = "AddDda")
    override fun add(documents: List<Document>, documentType: DocumentType): List<Document> {
        val ddaAgregado = DDAAgregadoTO(
            ddaagregado = DDAAgregado(
                inscricaoParceiro = configuration.getInscricaoParceiro(),
                tokenUsuario = configuration.userToken,
                inscricaotitular = configuration.getInscricaoTitular(),
                inscricaoagregado = documents.joinToString(separator = ",") { it.value },
                tipomanutencao = "I",
                tipopessoaagregado = if (documentType == DocumentType.CPF) "F" else "J",
            ),
        )
        return callDdaAgregado(documents, ddaAgregado)
    }

    @CircuitBreakerHoF(name = "BatchDda")
    override fun batchRemove(documents: List<Document>) = batchRequest(documents, MaintenanceType.Exclude)

    @CircuitBreakerHoF(name = "BatchDda")
    override fun batchAdd(documents: List<Document>) = batchRequest(documents, MaintenanceType.Include)

    private fun batchRequest(documents: List<Document>, maintenanceType: MaintenanceType): DDABatchResult {
        val ddaAgregado = DDAAgregadoTO(
            DDAAgregado(
                inscricaoParceiro = configuration.getInscricaoParceiro(),
                tokenUsuario = configuration.userToken,
                inscricaotitular = configuration.getInscricaoTitular(),
                inscricaoagregado = documents.joinToString(",") { it.value },
                tipomanutencao = maintenanceType.code,
            ),
        )

        val token = authenticationManager.getToken()

        val httpRequest = HttpRequest.POST(configuration.ddaCadastroLotePath, ddaAgregado)
            .header("client_id", configuration.clientId)
            .header("access_token", token)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)

        val marker = append("ddaAgregado", ddaAgregado)
            .andAppend("tipoManutencao", maintenanceType.code)

        try {
            val call = httpClient.exchange(
                httpRequest,
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.listOf(ArbiResponseTO::class.java),
            )

            val response = call.firstOrError().blockingGet()
            logger.info(
                marker.and(
                    append("response", response.body)
                        .andAppend("status", response.status),
                ),
                "batchRequestDDA",
            )

            return response.body()?.firstOrNull()?.idRequisicaoArbi?.let { DDABatchResult.Accepted(it) }
                ?: DDABatchResult.UnknownResponse
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)
            marker.andAppend("response", e.response.getBody(String::class.java))
            logger.error(marker, "batchRequestDDA")
            throw ArbiAdapterException()
        } catch (e: HttpClientException) {
            logger.error(marker, "batchRequestDDA", e)
            throw ArbiAdapterException()
        } catch (e: Exception) {
            logger.error(marker.andAppend("ACTION", "VERIFY"), "batchRequestDDA", e)
            throw ArbiAdapterException()
        }
    }

    @CircuitBreakerHoF(name = "QueryBatchRemove")
    override fun queryBatchRemove(idRequisicao: String): DDAQueryBatchRemoveResult {
        val token = authenticationManager.getToken()

        val uri = UriBuilder.of(configuration.ddaCadastroLotePath).path(idRequisicao).build()

        val httpRequest = HttpRequest.GET<Unit>(uri)
            .header("client_id", configuration.clientId)
            .header("access_token", token)
            .accept(MediaType.APPLICATION_JSON)

        val call = httpClient.retrieve(
            httpRequest,
            Argument.listOf(ArbiResponseTO::class.java),
            Argument.listOf(ArbiResponseTO::class.java),
        )

        val marker = append("idRequisicao", idRequisicao)

        try {
            val response = call.firstOrError().blockingGet()
            logger.info(marker.and(append("response", response)), "queryBatchDDA")

            val responseTO = response.firstOrNull() ?: return DDAQueryBatchRemoveResult.UnknownResponse

            val idStatus = responseTO.idStatus

            return when {
                idStatus == 102 -> DDAQueryBatchRemoveResult.ProcessingRequest
                idStatus == 200 && responseTO.resultado == "Todos os agregados estão Aptos." -> DDAQueryBatchRemoveResult.Success
                idStatus == 400 && listOf(
                    "Pedido pendendente",
                    "Pedido pendente",
                ).any { responseTO.descricaoStatus.contains(it) } -> DDAQueryBatchRemoveResult.PendingRequestError

                idStatus == 400 && responseTO.descricaoStatus.contains("erroPagador não encontrado") -> {
                    DDAQueryBatchRemoveResult.UnknownResponse
                }

                idStatus == 400 && responseTO.descricaoStatus.contains("cadastrado para o pagador") -> {
                    val regex = """erroAgregado de CPF/CNPJ (\d{11}) não cadastrado para o pagador""".toRegex()
                    regex.find(responseTO.descricaoStatus)?.let {
                        it.groups[1]?.let { group ->
                            DDAQueryBatchRemoveResult.PayerNotFound(document = group.value)
                        }
                    } ?: DDAQueryBatchRemoveResult.UnknownResponse
                }

                idStatus == 400 && (responseTO.resultado == null || !responseTO.resultado.startsWith('[')) -> DDAQueryBatchRemoveResult.UnknownResponse

                idStatus == 400 && !responseTO.resultado?.contains("existe").getOrFalse() -> {
                    val results = extractResult(responseTO)
                    DDAQueryBatchRemoveResult.Failed(results.map { Document(it.cpfCnpj) })
                }

                idStatus == 206 || idStatus == 400 -> responseTO.toRemoveResultPartialSuccess()
                else -> DDAQueryBatchRemoveResult.UnknownResponse
            }
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)
            marker.andAppend("response", e.response.getBody(String::class.java))
            logger.error(marker, "queryBatchRemove")
            throw ArbiAdapterException()
        } catch (e: HttpClientException) {
            logger.error(marker, "queryBatchRemove", e)
            throw ArbiAdapterException()
        } catch (e: Exception) {
            logger.error(marker.andAppend("ACTION", "VERIFY"), "queryBatchRemove", e)
            throw ArbiAdapterException()
        }
    }

    private fun callDdaAgregado(documents: List<Document>, ddaAgregado: DDAAgregadoTO): List<Document> {
        val documentAlreadyIncluded = listOf("Já existe/excluído na base.", "Agregado ja existe.")
        val documentSent = "Enviado"
        val partialSuccessResponseDescription =
            "Erro - Inclusão/Exclusão de Contas e/ou Agregados não preenchido Corrija a requisição e faça o reenvio."
        val ddaStillProcessingResponseDescription = listOf(
            "Erro - Pedido pendente para o pagador de CPF/CNPJ",
            "Erro - Pedido pendendente para o pagador de CPF/CNPJ",
        )
        val logName = "callDdaAgregado"

        val token = authenticationManager.getToken()
        val httpRequest = HttpRequest.POST(configuration.ddaCadastroPath, ddaAgregado)
            .header("client_id", configuration.clientId)
            .header("access_token", token)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        val call = httpClient.exchange(
            httpRequest,
            Argument.listOf(ArbiResponseTO::class.java),
            Argument.listOf(ArbiResponseTO::class.java),
        )
        val markers = append("documents", documents.map { it.value }).andAppend("ddaAgregado", ddaAgregado)
            .andAppend("tipoManutencao", ddaAgregado.ddaagregado.tipomanutencao)
        try {
            val response = call.firstOrError().blockingGet()
            markers.andAppend("response", response)

            if (response.status == HttpStatus.CREATED) {
                logger.info(markers.andAppend("documents", documents), logName)
                return documents
            }

            if (response.body().isEmpty()) {
                logger.error(
                    markers.andAppend("response", response.getBody(String::class.java))
                        .andAppend("emptyResponse", true),
                    logName,
                )
                return emptyList()
            }

            val (successDdaResults, errorDdaResults) = parseDdaResultResponse(
                response.body()!!.single().resultado,
                documentAlreadyIncluded + documentSent,
            )

            errorDdaResults.forEach {
                logger.error(append("ddaResult", it), "verifyCallDdaAgregado")
            }

            return successDdaResults.map {
                Document(it.cpfCnpj)
            }.also {
                logger.info(markers.andAppend("documents", it), logName)
            }
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)
            val response = e.response.getBody(Argument.listOf(ArbiResponseTO::class.java))
            markers.andAppend("response", e.response.getBody(String::class.java))
            if (response.isEmpty || response.get().isEmpty()) {
                logger.error(markers.andAppend("emptyResponse", true), logName)
                return emptyList()
            }
            val responseBody = response.get().first()

            if (ddaStillProcessingResponseDescription.any { errorMessage ->
                responseBody.descricaoStatus.contains(errorMessage)
            }
            ) {
                logger.warn(markers, logName)
                return emptyList()
            }

            if ("Agregado ja existe." == responseBody.descricaoStatus && documents.size == 1) {
                logger.info(markers, logName)
                return documents
            }

            if (partialSuccessResponseDescription in responseBody.descricaoStatus) {
                val (successDdaResults, errorDdaResults) = parseDdaResultResponse(
                    responseBody.resultado,
                    documentAlreadyIncluded,
                )

                errorDdaResults.forEach {
                    logger.error(append("ddaResult", it), "verifyCallDdaAgregado")
                }

                return successDdaResults.map {
                    Document(it.cpfCnpj)
                }.also {
                    logger.warn(markers.andAppend("documents", it), logName)
                }
            }

            logger.error(markers, logName)
            return emptyList()
        } catch (e: HttpClientException) {
            logger.error(markers, logName, e)
            throw ArbiAdapterException()
        } catch (e: Exception) {
            logger.error(markers.andAppend("ACTION", "VERIFY"), logName, e)
            throw ArbiAdapterException()
        }
    }

    private fun callDdaAgregado(document: Document, ddaAgregado: DDAAgregadoTO) {
        val token = authenticationManager.getToken()
        val httpRequest = HttpRequest.POST(configuration.ddaCadastroPath, ddaAgregado)
            .header("client_id", configuration.clientId)
            .header("access_token", token)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        val call = httpClient.retrieve(httpRequest, Argument.STRING, Argument.STRING)
        val marker = append("document", document.value)
            .andAppend("tipoManutencao", ddaAgregado.ddaagregado.tipomanutencao)

        try {
            val response = call.firstOrError().blockingGet()
            logger.info(marker.and(append("response", response)), "callDdaAgregado")
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)
            val response = e.response.getBody(String::class.java).get()
            marker.andAppend("response", response)
            if (ddaAgregado.ddaagregado.tipomanutencao == "I" && isDocumentAlreadyEnrolledInDda(response)) {
                logger.info(
                    marker.andAppend("status", "alreadyAdded"),
                    "callDdaAgregado",
                )
                return
            }
            if (ddaAgregado.ddaagregado.tipomanutencao == "E" && isDocumentNotEnrolledInDda(response)) {
                logger.info(
                    marker.andAppend("status", "alreadyRemoved"),
                    "callDdaAgregado",
                )
                return
            }

            if (e.status == HttpStatus.UNPROCESSABLE_ENTITY && listOf(
                    "Pedido pendendente para o pagador de CPF/CNPJ ",
                    "Pedido pendente para o pagador de CPF/CNPJ ",
                ).any { response.contains(it) }
            ) {
                logger.warn(marker, "callDdaAgregado", e)
            } else {
                logger.error(marker, "callDdaAgregado", e)
            }
            throw CallDDAAgregadoException(e)
        } catch (e: HttpClientException) {
            logger.error(marker, "callDdaAgregado", e)
            throw ArbiAdapterException()
        } catch (e: Exception) {
            logger.error(marker.andAppend("ACTION", "VERIFY"), "callDdaAgregado", e)
            throw CallDDAAgregadoException(e)
        }
    }

    private fun parseDdaResultResponse(
        response: String?,
        successResponses: List<String>,
    ): Pair<List<DdaResultTO>, List<DdaResultTO>> {
        val list: List<DdaResultTO> = mapper.readValue(
            response,
            mapper.typeFactory.constructCollectionType(List::class.java, DdaResultTO::class.java),
        )

        return list.partition { ddaResult ->
            successResponses.any { successResponse ->
                ddaResult.descricao.contains(successResponse)
            }
        }
    }

    private fun ArbiResponseTO.toRemoveResultPartialSuccess(): DDAQueryBatchRemoveResult.PartialSuccess {
        val (failed, success) = extractResult(this).partition {
            !it.descricao.startsWith("Já existe")
        }
        return DDAQueryBatchRemoveResult.PartialSuccess(
            failed = failed.map { Document(it.cpfCnpj) },
            success = success.map { Document(it.cpfCnpj) },
        )
    }

    @CircuitBreakerHoF(name = "QueryBatchAdd")
    override fun queryBatchAdd(idRequisicao: String): DDAQueryBatchAddResult {
        val token = authenticationManager.getToken()

        val uri = UriBuilder.of(configuration.ddaCadastroLotePath).path(idRequisicao).build()

        val httpRequest = HttpRequest.GET<Unit>(uri)
            .header("client_id", configuration.clientId)
            .header("access_token", token)
            .accept(MediaType.APPLICATION_JSON)

        val call = httpClient.retrieve(
            httpRequest,
            Argument.listOf(ArbiResponseTO::class.java),
            Argument.listOf(ArbiResponseTO::class.java),
        )

        val marker = append("idRequisicao", idRequisicao)

        try {
            val response = call.firstOrError().blockingGet()
            logger.info(marker.and(append("response", response)), "queryBatchDDA")

            val responseTO = response.firstOrNull() ?: return DDAQueryBatchAddResult.UnknownResponse

            val idStatus = responseTO.idStatus

            return when {
                idStatus == 102 -> DDAQueryBatchAddResult.ProcessingRequest
                idStatus == 200 && responseTO.resultado == "Todos os agregados estão Aptos." -> DDAQueryBatchAddResult.Success
                idStatus == 400 && listOf(
                    "Pedido pendendente",
                    "Pedido pendente",
                ).any { responseTO.descricaoStatus.contains(it) } -> DDAQueryBatchAddResult.PendingRequestError

                idStatus == 400 && responseTO.descricaoStatus.contains("já cadastrado para o pagador") -> {
                    if (responseTO.resultado == "Todos os agregados estão Aptos.") {
                        return DDAQueryBatchAddResult.Success
                    } else {
                        builPartialSucess(responseTO)
                    }
                }

                idStatus == 400 && !responseTO.resultado?.contains("existe").getOrFalse() -> {
                    val results = extractResult(responseTO)
                    DDAQueryBatchAddResult.Failed(results.map { Document(it.cpfCnpj) })
                }

                idStatus == 206 || idStatus == 400 -> builPartialSucess(responseTO)
                else -> DDAQueryBatchAddResult.UnknownResponse
            }
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)
            marker.andAppend("response", e.response.getBody(String::class.java))
            logger.error(marker, "queryBatchDDA")
            throw ArbiAdapterException()
        } catch (e: HttpClientException) {
            logger.error(marker, "queryBatchDDA", e)
            throw ArbiAdapterException()
        } catch (e: Exception) {
            logger.error(marker.andAppend("ACTION", "VERIFY"), "queryBatchDDA", e)
            throw ArbiAdapterException()
        }
    }

    private fun extractResult(responseTO: ArbiResponseTO): List<DdaResultTO> {
        return mapper.readValue(
            responseTO.resultado,
            mapper.typeFactory.constructCollectionType(List::class.java, DdaResultTO::class.java),
        )
    }

    private fun builPartialSucess(responseTO: ArbiResponseTO): DDAQueryBatchAddResult.PartialSuccess {
        val (failed, success) = extractResult(responseTO).partition {
            it.status == "Erro" && !it.descricao.startsWith("Já existe")
        }
        return DDAQueryBatchAddResult.PartialSuccess(
            failed = failed.map { Document(it.cpfCnpj) },
            success = success.map { Document(it.cpfCnpj) },
        )
    }

    override fun remove(document: Document) {
        val ddaAgregado = DDAAgregadoTO(
            ddaagregado = DDAAgregado(
                inscricaoParceiro = configuration.getInscricaoParceiro(),
                tokenUsuario = configuration.userToken,
                inscricaotitular = configuration.getInscricaoTitular(),
                inscricaoagregado = document.value,
                tipomanutencao = "E",
                tipopessoaagregado = if (document.type == DocumentType.CPF) "F" else "J",
            ),
        )
        callDdaAgregado(document, ddaAgregado)
    }

    private fun isDocumentAlreadyEnrolledInDda(response: String): Boolean {
        return response.contains(
            "Agregado ja existe",
            true,
        ) || response.contains("já cadastrado para o pagador", true)
    }

    private fun isDocumentNotEnrolledInDda(response: String): Boolean {
        return response.contains(
            "Agregado não existe.",
            true,
        )
    }

    fun convertToBankStatement(map: Map<String, String>, document: String): DefaultBankStatementItem {
        val updateDateFormat: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        val counterpartDocument = map.getOrDefault("inscricaocontraparte", "").padStart(11, '0')
        val type = getBankStatementType(map.get("codhist")!!, document == counterpartDocument)
        val counterpartAccountNo = if (type == BankStatementItemType.TRANSFERENCIA_CC) {
            map.getOrDefault("finalidade", null)
                ?.let {
                    when (val parsed = TefFinalidadeParseResult.create(it)) {
                        TefFinalidadeParseResult.Invalid -> {
                            logger.error(append("finalidade", it), "ArbiStatement")
                            null
                        }

                        is TefFinalidadeParseResult.Valid -> parsed.counterpartAccountNo
                    }
                }
        } else {
            null
        }
        val lastUpdate = map.getOrDefault("dataatualizacao", null)?.let {
            LocalDateTime.parse(it, updateDateFormat).atZone(brazilTimeZone)
        }
        val historico = map.getOrDefault("historico", "")
        val operationNumber = map.getOrDefault("nromovimento", "")

        return DefaultBankStatementItem(
            date = LocalDate.parse(map.getOrElse("datamovimento") { throw IllegalStateException() }, arbiDateFormat),
            flow = if (map["natureza"].equals("C")) {
                BankStatementItemFlow.CREDIT
            } else {
                BankStatementItemFlow.DEBIT // FIXME check for "D"
            },
            type = type,
            description = if (type == BankStatementItemType.INVESTMENT_REDEMPTION) {
                map.getOrDefault("finalidade", historico)
            } else {
                historico
            },
            operationNumber = operationNumber,
            amount = convertToLong(map["valor"]!!),
            counterpartName = map.getOrDefault("nomecontraparte", ""),
            counterpartDocument = counterpartDocument.trim(),
            counterpartAccountNo = counterpartAccountNo,
            documentNumber = map.getOrDefault("nrodocto", ""),
            lastUpdate = lastUpdate,
        )
    }

    private fun getBankStatementType(code: String, sameOwnership: Boolean): BankStatementItemType {
        return when (code) {
            "00176" -> BankStatementItemType.TED_DIF_TITULARIDADE
            "00055" -> if (sameOwnership) BankStatementItemType.TED_MESMA_TITULARIDADE else BankStatementItemType.TED_DIF_TITULARIDADE
            "00156", "00057" -> BankStatementItemType.TED_MESMA_TITULARIDADE
            "00058" -> BankStatementItemType.DEVOLUCAO_TED
            "00102", "00259", "00213", "00011" -> BankStatementItemType.TRANSFERENCIA_CC
            "00264", "00265", "00266", "00267", "00319" -> BankStatementItemType.PIX
            "00268", "00269" -> BankStatementItemType.DEVOLUCAO_PIX
            "00558", "00632" -> BankStatementItemType.INVESTMENT_REDEMPTION
            "00029", "00197" -> BankStatementItemType.PAGAMENTO_BOLETO
            else -> BankStatementItemType.OUTROS
        }
    }

    private fun isUnauthorizedBankTransfer(e: HttpClientResponseException): Boolean {
        if (e.status == HttpStatus.UNAUTHORIZED) {
            logger.warn(append("error_message", "Token is expired"), "ArbiValidate")
            authenticationManager.cleanTokens()
            return true
        }
        return false
    }

    private fun unknownTransferResponse(
        marker: LogstashMarker,
        e: Exception,
        operationId: BankOperationId,
        amount: Long,
    ): BankTransfer {
        logger.error(marker, "ArbiTransfer", e)
        return BankTransfer(
            operationId = operationId,
            status = BankOperationStatus.UNKNOWN,
            amount = amount,
            errorDescription = "Erro ao executar transferencia",
            gateway = FinancialServiceGateway.ARBI,
        )
    }

    private fun readArbiExceptionResponse(
        marker: LogstashMarker,
        httpClientResponseException: HttpClientResponseException,
    ): Optional<MutableList<ArbiResponseTO>> {
        return try {
            httpClientResponseException.response.getBody(Argument.listOf(ArbiResponseTO::class.java))
        } catch (e: Exception) {
            val body = httpClientResponseException.response.getBody(String::class.java).orElse("")
            logger.error(
                marker.and<LogstashMarker>(append("status", httpClientResponseException.status))
                    .and(append("body", body)),
                "ArbiTransfer",
                e,
            )
            throw e
        }
    }

    internal fun getFirstWorkingPaymentDate(hourLimitString: String = configuration.paymentTimeLimit): String {
        val now = getZonedDateTime()
        val hourLimit: LocalTime = LocalTime.parse(hourLimitString, timeFormat)
        val date = if (now.toLocalTime() >= hourLimit) now.plusDays(1) else now
        return getFirstWorkingDate(date.toLocalDate()).format(DateTimeFormatter.ISO_LOCAL_DATE)
    }

    internal fun getFirstWorkingDate(from: LocalDate = getLocalDate()): LocalDate {
        val holidays = FinancialInstitutionGlobalData.holidays
        var workingDate = from

        while (workingDate.dayOfWeek in listOf(
                DayOfWeek.SATURDAY,
                DayOfWeek.SUNDAY,
            ) || holidays.contains(workingDate)
        ) {
            workingDate = workingDate.plusDays(1)
        }
        return workingDate
    }

    private fun getBills(dda: DDATO): List<DDAItem> {
        val token = authenticationManager.getToken()
        val markers = append("dda", dda)
        val httpRequest = HttpRequest.POST(configuration.ddaV2Path, dda)
            .header("client_id", configuration.clientId)
            .header("access_token", token)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.listOf(ArbiResponseTO::class.java),
            Argument.STRING,
        )

        var response: List<ArbiResponseTO>? = null
        try {
            response = call.firstOrError().blockingGet()

            if (response.isNotEmpty() && response.first().resultado == "Sem movimentos para o período informado.") {
                logger.info(markers.andAppend("response", "SEM_MOVIMENTOS"), "DDAGetBills")
                return emptyList()
            }

            @Suppress("UNCHECKED_CAST")
            val mappedResponse: List<Map<String, String>> = response.map {
                jacksonObjectMapper().enable(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature()).readValue(
                    it.resultado,
                    Map::class.java,
                )
            } as List<Map<String, String>>
            return mappedResponse.map { convertToDDABill(it) }.also { items ->
                logger.info(
                    markers.andAppend(
                        "digitableLines",
                        items.map {
                            it.barcode.digitable
                        },
                    ),
                    "DDAGetBills",
                )
            }
        } catch (e: HttpClientResponseException) {
            if (e.status == HttpStatus.NO_CONTENT) {
                logger.info(markers.andAppend("response", "EMPTY_BODY"), "DDAGetBills", e)
                return emptyList()
            }

            checkUnauthorized(e)
            logger.error(
                markers.andAppend("response", e.response.getBody(String::class.java).get()),
                "ArbiGetDDABills",
                e,
            )
            throw ArbiAdapterException()
        } catch (e: HttpClientException) {
            logger.error(markers, "ArbiGetDDABills", e)
            throw ArbiAdapterException()
        } catch (e: Exception) {
            markers.andAppend("response", response)
            logger.error(markers.andAppend("ACTION", "VERIFY"), "ArbiGetDDABills", e)
            throw ArbiAdapterException()
        }
    }

    private fun convertToDDABill(map: Map<String, String>): DDAItem {
        return DDAItem(
            amount = map["valor"]?.toDouble() ?: 0.0,
            barcode = BarCode(number = map["codbarras"] as String, digitable = map["linhadigitavel"] as String),
            document = map.getOrElse("inscricaosacado") { throw IllegalStateException() },
            dueDate = LocalDate.parse(
                map.getOrElse("datavctotitulo") { throw IllegalStateException() },
                arbiDateFormat,
            ),
            ddaProvider = DDAProvider.ARBI,
        )
    }

    private fun fetchValidate(barCode: BarCode): BillValidationResponse {
        val token = authenticationManager.getToken()
        val billValidateRequestTO = BillValidateRequestTO(
            pagamentos = PagamentosTO(
                inscricaoParceiro = configuration.inscricao,
                tokenUsuario = configuration.userToken,
                codigoBarras = barCode.number,
                contaOrigem = configuration.contaLiquidacao,
                dataPgto = getFirstWorkingPaymentDate(),
            ),
        )
        val httpRequest = HttpRequest.POST(configuration.validateV2Path, billValidateRequestTO)
            .header("client_id", configuration.clientId)
            .header("access_token", token)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.listOf(ArbiResponseTO::class.java),
            Argument.listOf(ArbiResponseTO::class.java),
        )
        return try {
            val response = call.firstOrError().blockingGet()
            logger.info(
                append("request", billValidateRequestTO)
                    .andAppend("response", response),
                "ArbiValidate",
            )

            response[0].resultado?.let {
                convertToValidationResponse(it)
            } ?: throw BoletoSettlementException(FinancialServiceGateway.ARBI, "resultado is null")
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)
            convertToValidationResponse(billValidateRequestTO, e)
        } catch (e: HttpClientException) {
            logger.error("ArbiValidate", e)
            throw ArbiAdapterException()
        } catch (e: Exception) {
            logger.error(append("ACTION", "VERIFY"), "ArbiValidate", e)
            throw ArbiAdapterException()
        }
    }

    internal fun convertToValidationResponse(result: String): BillValidationResponse {
        val jacksonObjectMapper = jacksonObjectMapper()
        val response: MutableMap<String, String> = mutableMapOf()
        val tree = jacksonObjectMapper.readTree(result)
        if (tree.isArray) {
            tree.asIterable().forEach { node ->
                @Suppress("UNCHECKED_CAST")
                val billDetails = jacksonObjectMapper.treeToValue(
                    node.get(node.fieldNames().next()),
                    Map::class.java,
                ) as Map<String, String>
                response.putAll(billDetails)
            }
        }

        if (!response["codigoerro"].isNullOrEmpty()) {
            return ArbiValidationResponse(response["codigoerro"] + ": " + response["descricaoerro"].orEmpty())
        }

        val assignor = FinancialInstitutionGlobalData.bankList.firstOrNull {
            it.number == response["CodPartDestinatario"]!!.toLong()
        }?.name.orEmpty()

        val partialPaymentType = when (response["TpAutcRecbtVlrDivgte"]) {
            "1" -> PartialPaymentAmountType.ANY_VALUE
            "2" -> PartialPaymentAmountType.BETWEEN_MINIMUM_AND_MAXIMUM
            "3" -> PartialPaymentAmountType.NOT_ACCEPT_DIFFERENT_VALUE
            "4" -> PartialPaymentAmountType.ONLY_MINIMUM_VALUE
            else -> null
        }

        val divergentPayment = when (partialPaymentType) {
            PartialPaymentAmountType.ANY_VALUE, PartialPaymentAmountType.BETWEEN_MINIMUM_AND_MAXIMUM, PartialPaymentAmountType.ONLY_MINIMUM_VALUE -> {
                val partial = if (response["TpVlr_PercMinTit"] == "V") {
                    DivergentPayment(
                        minimumAmount = response["Vlr_PercMinTit"]?.let { convertToLongOrNull(response["Vlr_PercMinTit"]) },
                        minimumPercentage = null,
                        amountType = partialPaymentType,
                    )
                } else {
                    DivergentPayment(
                        minimumAmount = null,
                        minimumPercentage = response["Vlr_PercMinTit"]?.toBigDecimalOrNull(),
                        amountType = partialPaymentType,
                    )
                }

                if (response["TpVlr_PercMaxTit"] == "V") {
                    partial.copy(
                        maximumAmount = response["Vlr_PercMaxTit"]?.let { convertToLongOrNull(response["Vlr_PercMaxTit"]) },
                        maximumPercentage = null,
                    )
                } else {
                    partial.copy(
                        maximumAmount = null,
                        maximumPercentage = response["Vlr_PercMaxTit"]?.toBigDecimalOrNull(),
                    )
                }
            }

            PartialPaymentAmountType.NOT_ACCEPT_DIFFERENT_VALUE -> DivergentPayment(amountType = PartialPaymentAmountType.NOT_ACCEPT_DIFFERENT_VALUE)
            else -> null
        }

        val chain = getRecipientChain(response)
        return ArbiValidationResponse(
            billRegisterData = BillRegisterData(
                billType = BillType.FICHA_COMPENSACAO,
                assignor = assignor,
                recipient = chain.getRecipientByPayer(response["CNPJ_CPFPagdr"]!!, assignor),
                recipientChain = chain,
                payerDocument = formatDocument(response["CNPJ_CPFPagdr"], response["TpPessoaPagdr"]),
                payerName = response["Nom_RzSocPagdr"],
                amount = convertToLongOrException(response["valorTituloOriginal"].takeIf { !it.isNullOrBlank() } ?: response["VlrTit"]),
                discount = convertToLongOrZero(response["VlrCalcdDesct"] ?: response["valorDescontoCalculado"]),
                interest = convertToLongOrZero(response["VlrCalcdJuros"] ?: response["valorJurosCalculado"]),
                fine = convertToLongOrZero(response["VlrCalcdMulta"] ?: response["valorMultaCalculado"]),
                amountTotal = convertToLongOrException(response["VlrTotCobrar"] ?: response["valorTotalCobrar"]),
                expirationDate = formatDate(response["DtLimPgtoTit"])!!,
                settleDate = getLocalDate(),
                paymentLimitTime = paymentTimeLimit,
                dueDate = formatDate(response["DtVencTit"])!!,
                amountCalculationModel = convertToAmountCalculationModel(response["TpModlCalc"]),
                fichaCompensacaoType = FichaCompensacaoType.findByCode((response["CodEspTit"])?.toInt()),
                amountPaid = calculateAmountPaid(response),
                idNumber = response["NumIdentcTit"],
                registrationUpdateNumber = response["NumSeqAtlzCadTit"]?.takeIf { it.isNotBlank() }?.toLong(),
                interestData = InterestData(
                    type = response["CodJurosTit"]?.let { InterestType.getByCode(it) },
                    value = response["Vlr_PercJurosTit"]?.toBigDecimalOrNull(),
                    date = formatDate(response["DtJurosTit"]),
                ),
                fineData = FineData(
                    type = response["CodMultaTit"]?.let { FineType.getByCode(it) },
                    value = response["Vlr_PercMultaTit"]?.toBigDecimalOrNull(),
                    date = formatDate(response["DtMultaTit"]),
                ),
                discountData = DiscountData(
                    type = response["CodDesctTit"]?.let { DiscountType.getByCode(it) },
                    value1 = response["Vlr_PercDesctTit"]?.toBigDecimalOrNull(),
                    date1 = formatDate(response["DtDesctTit"]),
                    value2 = null, // TODO não da pra preencher pois não é devolvido. Podemos perguntar se o desconto na consulta sempre é o ativo
                    date2 = null,
                    value3 = null,
                    date3 = null,
                ),
                abatement = response["VlrAbattTit"]?.toDouble(),
                rebate = convertToLongOrZero(response["VlrAbattTit"]),
                divergentPayment = divergentPayment!!,
                partialPayment = PartialPayment(
                    acceptPartialPayment = response["IndrPgtoParcl"] == "S",
                    qtdPagamentoParcial = convertToIntOrZero(response["QtdPgtoParcl"]),
                    qtdPagamentoParcialRegistrado = convertToIntOrZero(response["QtdPgtoParclRegtd"]),
                    saldoAtualPagamento = convertToLongOrZero(response["VlrSldTotAtlPgtoTit"]),
                ),
            ),
            paymentStatus = response["SitTitPgto"]!!.toInt(),
            resultado = ArbiValidationResponse.convertStatusToErrorDescription(response["SitTitPgto"]!!.toInt()),
        )
    }

    private fun formatDate(strDate: String?) =
        strDate?.takeIf { it.isNotBlank() }?.let { LocalDate.parse(it, DateTimeFormatter.ISO_DATE) }

    private fun convertToValidationResponse(
        billValidateRequestTO: BillValidateRequestTO,
        e: HttpClientResponseException,
    ): ArbiValidationResponse {
        val response = e.response.getBody(Argument.listOf(ArbiResponseTO::class.java))
        if (response.isPresent) {
            val errorDescription = response.get()[0].descricaoStatus
            val markers = Markers.appendArray("response", *response.get().toTypedArray())
                .andAppend("status", e.status)
                .andAppend("request", billValidateRequestTO)
            if (isExpectedResponse(errorDescription)) {
                logger.warn(markers, "ArbiValidate")
            } else {
                logger.error(markers, "ArbiValidate")
            }
            return ArbiValidationResponse(errorDescription)
        }
        if (e.response.getBody(Argument.STRING).get().contains("Requisição não realizada")) {
            logger.error(
                append("status", e.status)
                    .andAppend("request", billValidateRequestTO)
                    .andAppend("response", e.response.getBody(Argument.STRING)),
                "ArbiValidate",
            )
            return ArbiValidationResponse("Requisição não realizada")
        }
        logger.error(
            append("status", e.status)
                .andAppend("request", billValidateRequestTO)
                .andAppend("response", e.response.getBody(Argument.STRING)),
            "ArbiValidate",
            e,
        )
        throw BoletoSettlementException(gateway = FinancialServiceGateway.ARBI, e = e)
    }

    private fun isExpectedResponse(errorDescription: String): Boolean {
        val errorMessages = listOf(
            "1 - Timeout alcancado 0",
            "Mensagem rejeitada pela CIP: EDDA0526",
        )

        return errorMessages.any { errorDescription.contains(it) }
    }

    private fun formatDocument(document: String?, type: String?): String? {
        if (document.isNullOrEmpty()) {
            return document
        }
        return when (type) {
            "F" -> document.padStart(11, '0')
            "J" -> document.padStart(14, '0')
            else -> ""
        }
    }

    private fun convertToAmountCalculationModel(tipoModeloCalculo: String?) = when (tipoModeloCalculo) {
        "01" -> AmountCalculationModel.ANYONE
        "02" -> AmountCalculationModel.BENEFICIARY_AFTER_DUE_DATE
        "03" -> AmountCalculationModel.BENEFICIARY_ONLY
        "04" -> AmountCalculationModel.ON_DEMAND
        else -> {
            logger.error(append("TpModlCalc", tipoModeloCalculo), "ArbiValidate")
            AmountCalculationModel.UNKNOWN
        }
    }

    private fun calculateAmountPaid(response: MutableMap<String, String>): Long? {
        val amountPaid = response["VlrBaixaEftTit"] ?: response["VlrBaixaOperacTit"]
        val canPartiallyPay = when (response["IndrPgtoParcl"]) {
            "S" -> true
            "N" -> false
            else -> {
                logger.error(append("ACTION", "VERIFY"), "ArbiValidate")
                throw Exception("could not identify if is partial account")
            }
        }

        if (!amountPaid.isNullOrEmpty()) {
            logger.info(append("amountPaid", amountPaid).andAppend("amountPaidType", "baixas"), "ArbiValidate#calculateAmountPaid")
            return convertToLongOrException(amountPaid)
        }

        if (canPartiallyPay) {
            val valorSaldoTotalAtualPagamentoTitulo =
                response["VlrSldTotAtlPgtoTit"].takeIf { !it.isNullOrBlank() } ?: return null
            logger.info(
                append("amountPaid", valorSaldoTotalAtualPagamentoTitulo).andAppend(
                    "amountPaidType",
                    "valorParcial",
                ),
                "ArbiValidate#calculateAmountPaid",
            )
            return convertToLongOrException(valorSaldoTotalAtualPagamentoTitulo)
        } else {
            return when (response["SitTitPgto"]?.toInt()) {
                // Boleto pago sem baixa. Assumimos que o valor devido é o valor do pagamento
                CIPSitTitPgto.PAGAMENTO_JA_EFETUADO.code -> {
                    logger.info(
                        append("amountPaid", response["valorTotalCobrar"]).andAppend(
                            "amountPaidType",
                            "valorTotalCobrar",
                        ),
                        "ArbiValidate#calculateAmountPaid",
                    )

                    convertToLongOrException(
                        response["valorTotalCobrar"],
                    )
                }

                else -> {
                    null
                }
            }
        }
    }

    fun calculateBillAmount(billAmountCalculationRequest: BillAmountCalculationRequest): BillAmountCalculationResponse? {
        fun AmountCalculationModel.getFormattedCode() = code.toInt().toString()
        fun AmountCalculationData.getFormattedValue() = value?.toBigDecimal()?.toPlainString().orEmpty()

        val token = authenticationManager.getToken()

        val calculatorRequestTO = with(billAmountCalculationRequest) {
            CalculatorRequestTO(
                calculadora = CalculadoraTO(
                    inscricaoParceiro = configuration.inscricao,
                    tokenUsuario = configuration.userToken,
                    modeloCalculo = amountCalculationModel.getFormattedCode(),
                    valorTitulo = convertToString(originalAmount),
                    dataVencimentoTitulo = billAmountCalculationRequest.dueDate.format(dateFormat),
                    dataOperacao = getZonedDateTime().format(dateFormat),
                    tipoJuros = interest.type,
                    valorJuros = interest.getFormattedValue(),
                    dataJuros = interest.date?.format(dateFormat) ?: "",
                    tipoMulta = fine.type,
                    valorMulta = fine.getFormattedValue(),
                    dataMulta = fine.date?.format(dateFormat) ?: "",
                    tipoDesconto1 = discount1.type,
                    dataDesconto1 = discount1.date?.format(dateFormat) ?: "",
                    valorDesconto1 = discount1.getFormattedValue(),
                    tipoDesconto2 = discount2?.type.orEmpty(),
                    dataDesconto2 = discount2?.date?.format(dateFormat) ?: "",
                    valorDesconto2 = discount2?.getFormattedValue() ?: "",
                    tipoDesconto3 = discount3?.type.orEmpty(),
                    dataDesconto3 = discount3?.date?.format(dateFormat) ?: "",
                    valorDesconto3 = discount3?.getFormattedValue() ?: "",
                    valorAbatimento = abatement.toBigDecimal().toPlainString(),
                ),
            )
        }

        val httpRequest = HttpRequest.POST(configuration.calculatePath, calculatorRequestTO)
            .header("client_id", configuration.clientId)
            .header("access_token", token)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        val call = httpClient.exchange(
            httpRequest,
            Argument.listOf(ArbiCalulatorResponseTO::class.java),
            Argument.STRING,
        )

        val marker = append("calculatorRequestTO", calculatorRequestTO)

        return try {
            val httpResponse = call.firstOrError().blockingGet()

            val body = httpResponse.body()?.singleOrNull()
            if (body == null) {
                logger.error(
                    marker.andAppend("httpResponse", httpResponse.getBody(String::class.java)),
                    "ArbiCalculator",
                )
                return null
            }

            return BillAmountCalculationResponse(
                calculatedAmount = convertToLong(body.totalCalculatedAmount),
                calculatedFinesAmount = convertToLong(body.calculatedFineAmount),
                calculatedInterestAmount = convertToLong(body.calculatedInterestAmount),
                calculatedDiscountAmount = convertToLong(body.calculatedDiscountAmount),
            ).also {
                logger.info(
                    marker.andAppend("httpResponse", body)
                        .andAppend("calculationResponse", it),
                    "ArbiCalculator",
                )
            }
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)
            logger.error(marker.andAppend("response", e.response), "ArbiCalculator")
            null
        } catch (e: Exception) {
            logger.error(marker, "ArbiCalculator", e)
            null
        }
    }

    data class CalculatorRequestTO(
        val calculadora: CalculadoraTO,
    )

    @JsonInclude(JsonInclude.Include.ALWAYS)
    data class CalculadoraTO(
        @JsonProperty("inscricaoparceiro") val inscricaoParceiro: String,
        @JsonProperty("tokenusuario") val tokenUsuario: String,
        @JsonProperty("idrequisicao") val idRequisicao: String = UUID.randomUUID().toString(),
        @JsonProperty("idmodulo") val idModulo: String = "2",
        @JsonProperty("idtransacao") val idTransacao: String = "3",
        @JsonProperty("modelocalculo") val modeloCalculo: String,
        @JsonProperty("codespecietitulo") val codEspecieTitulo: String = "",
        @JsonProperty("codpraca") val codPraca: String = "00000",
        @JsonProperty("tipojuros") val tipoJuros: String,
        @JsonProperty("tipomulta") val tipoMulta: String,
        @JsonProperty("datavencimentotitulo") val dataVencimentoTitulo: String,
        @JsonProperty("dataoperacao") val dataOperacao: String,
        @JsonProperty("datajuros") val dataJuros: String = "",
        @JsonProperty("datamulta") val dataMulta: String = "",
        @JsonProperty("valortitulo") val valorTitulo: String,
        @JsonProperty("valorjuros") val valorJuros: String = "",
        @JsonProperty("valormulta") val valorMulta: String = "",
        @JsonProperty("valorabatimento") val valorAbatimento: String,
        @JsonProperty("valorsaldototalatual") val valorSaldoTotalAtual: String = "",
        @JsonProperty("valorjuroscalculado1") val valorJurosCalculado1: String = "",
        @JsonProperty("valormultacalculado1") val valorMultaCalculado1: String = "",
        @JsonProperty("valordescontocalculado1") val valorDescontoCalculado1: String = "",
        @JsonProperty("valortotalcobrar1") val valorTotalCobrar1: String = "",
        @JsonProperty("datavalidadecalculo1") val dataValidadeCalculo1: String = "",
        @JsonProperty("valorjuroscalculado2") val valorJurosCalculado2: String = "",
        @JsonProperty("valormultacalculado2") val valorMultaCalculado2: String = "",
        @JsonProperty("valordescontocalculado2") val valorDescontoCalculado2: String = "",
        @JsonProperty("valortotalcobrar2") val valorTotalCobrar2: String = "",
        @JsonProperty("datavalidadecalculo2") val dataValidadeCalculo2: String = "",
        @JsonProperty("valorjuroscalculado3") val valorJurosCalculado3: String = "",
        @JsonProperty("valormultacalculado3") val valorMultaCalculado3: String = "",
        @JsonProperty("valordescontocalculado3") val valorDescontoCalculado3: String = "",
        @JsonProperty("valortotalcobrar3") val valorTotalCobrar3: String = "",
        @JsonProperty("datavalidadecalculo3") val dataValidadeCalculo3: String = "",
        @JsonProperty("valorjuroscalculado4") val valorJurosCalculado4: String = "",
        @JsonProperty("valormultacalculado4") val valorMultaCalculado4: String = "",
        @JsonProperty("valordescontocalculado4") val valorDescontoCalculado4: String = "",
        @JsonProperty("valortotalcobrar4") val valorTotalCobrar4: String = "",
        @JsonProperty("datavalidadecalculo4") val dataValidadeCalculo4: String = "",
        @JsonProperty("valorjuroscalculado5") val valorJurosCalculado5: String = "",
        @JsonProperty("valormultacalculado5") val valorMultaCalculado5: String = "",
        @JsonProperty("valordescontocalculado5") val valorDescontoCalculado5: String = "",
        @JsonProperty("valortotalcobrar5") val valorTotalCobrar5: String = "",
        @JsonProperty("datavalidadecalculo5") val dataValidadeCalculo5: String = "",
        @JsonProperty("tipodesconto1") val tipoDesconto1: String,
        @JsonProperty("datadesconto1") val dataDesconto1: String = "",
        @JsonProperty("valordesconto1") val valorDesconto1: String = "",
        @JsonProperty("tipodesconto2") val tipoDesconto2: String = "",
        @JsonProperty("datadesconto2") val dataDesconto2: String = "",
        @JsonProperty("valordesconto2") val valorDesconto2: String = "",
        @JsonProperty("tipodesconto3") val tipoDesconto3: String = "",
        @JsonProperty("datadesconto3") val dataDesconto3: String = "",
        @JsonProperty("valordesconto3") val valorDesconto3: String = "",
    )

    override fun register(zipCode: ZipCode) {
        val markers = append("zipCode", zipCode.value)
        val token = authenticationManager.getToken()
        val httpRequest =
            HttpRequest.GET<ArbiZipCodeRegisterResponseTO>("${configuration.cadastroEnderecoPath}?cep=${zipCode.value}")
                .header("client_id", configuration.clientId)
                .header("access_token", token)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON_TYPE)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(ArbiZipCodeRegisterResponseTO::class.java),
            Argument.STRING,
        )
        try {
            val response = call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", response), "ArbiRegisterZipCode")
        } catch (e: HttpClientResponseException) {
            val response = e.response.getBody(String::class.java).orElse("")
            markers.andAppend("response", response).andAppend("status", e.status)
            checkUnauthorized(e)
            logger.error(markers, "ArbiRegisterZipCode", e)
            throw ArbiAdapterException()
        } catch (e: HttpClientException) {
            logger.error(markers, "ArbiRegisterZipCode", e)
            throw ArbiAdapterException()
        } catch (e: Exception) {
            logger.error(markers.andAppend("ACTION", "VERIFY"), "ArbiRegisterZipCode", e)
            throw ArbiAdapterException()
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ArbiAdapter::class.java)
    }
}

internal fun getScheduleDate(): String {
    var date = getZonedDateTime()
    while (date.toLocalDate().dayOfWeek in listOf(
            DayOfWeek.SATURDAY,
            DayOfWeek.SUNDAY,
        ) || FinancialInstitutionGlobalData.holidays.contains(date.toLocalDate())
    ) {
        date = date.minusDays(1)
    }

    return date.format(DateTimeFormatter.ISO_LOCAL_DATE)
}

fun convertToIntOrZero(value: String?): Int {
    if (value.isNullOrBlank()) {
        return 0
    }

    return value.toInt()
}

fun convertToLongOrZero(value: String?): Long {
    if (value.isNullOrBlank()) {
        return 0
    }

    return convertToLong(value)
}

fun convertToLongOrNull(value: String?): Long? {
    if (value.isNullOrBlank()) {
        return null
    }

    return convertToLong(value)
}

fun convertToLong(value: String): Long {
    if (value.isBlank()) {
        throw ArbiAdapterException()
    }

    val nf = NumberFormat.getNumberInstance(Locale.US) as DecimalFormat
    nf.applyPattern("#.##")
    return (nf.parse(value).toDouble() * 100.0).roundToLong()
}

fun convertToLongOrException(value: String?): Long {
    if (value.isNullOrBlank()) {
        throw ArbiAdapterException()
    }

    return convertToLong(value)
}

fun convertToString(value: Double): String {
    return value.toBigDecimal().setScale(2, RoundingMode.FLOOR).toPlainString()
}

fun convertToString(value: Long): String {
    return value.toBigDecimal().divide(BigDecimal(100)).setScale(2, RoundingMode.FLOOR).toPlainString()
}

@ConfigurationProperties("integrations.arbi")
class ArbiConfiguration {
    lateinit var host: String
    lateinit var newHost: String
    lateinit var grantCodePath: String
    lateinit var accessTokenPath: String
    lateinit var validatePath: String
    lateinit var validateV2Path: String
    lateinit var calculatePath: String
    lateinit var checkingPath: String
    lateinit var tedStatusPath: String
    lateinit var checkingV2Path: String
    lateinit var paymentV2Path: String
    lateinit var getStatementV2Path: String
    lateinit var tedStatusV2Path: String
    lateinit var accountStatementPath: String
    lateinit var ddaPath: String
    lateinit var ddaV2Path: String
    lateinit var ddaCadastroPath: String
    lateinit var ddaCadastroLotePath: String
    lateinit var cadastroPFPath: String
    lateinit var accountStatus: String
    lateinit var domainPath: String
    lateinit var domainV2Path: String
    lateinit var encerrarContaV2Path: String
    lateinit var cadastroEnderecoPath: String
    lateinit var devicesPath: String
    lateinit var userToken: String
    lateinit var clientId: String
    lateinit var clientSecret: String
    lateinit var contaTitular: String
    lateinit var contaCashin: String
    lateinit var contaLiquidacao: String
    lateinit var inscricao: String
    var inscricaoParceiro: String? = null
    var inscricaoTitular: String? = null
    lateinit var tipoPessoa: String
    lateinit var paymentTimeLimit: String
    lateinit var informeDeRendimentosPath: String
    var gatewayV2ContaCorrente: Boolean = false
}

fun ArbiConfiguration.getInscricaoParceiro(): String {
    return inscricaoParceiro ?: inscricao
}

fun ArbiConfiguration.getInscricaoTitular(): String {
    return inscricaoTitular ?: inscricao
}

data class DDATO(val dda: DDA)

data class DDA(
    @JsonProperty("inscricaoparceiro") val inscricaoParceiro: String,
    @JsonProperty("tokenusuario") val tokenUsuario: String,
    @JsonProperty("idrequisicao") val idRequisicao: IdRequisicao = IdRequisicao(),
    @JsonProperty("idmodulo") val idModulo: String = "2",
    @JsonProperty("idtransacao") val idTransacao: String = "8",
    @JsonProperty("bancotitular") val bancoTitular: String = ARBI_BANK_NO,
    @JsonProperty("agenciatitular") val agenciaTitular: String = ARBI_ROUTING_NO,
    @JsonProperty("contatitular") val contaTitular: String,
    @JsonProperty("inscricao") val inscricao: String,
    @JsonProperty("tipopessoa") val tipoPessoa: String,
    @JsonProperty("tipomanutencao") val tipoManutencao: String = "",
    @JsonProperty("grupoagregado") val grupoAgregado: String = "",
    @JsonProperty("tiporetornotitulos") val tipoRetornoTitulos: String,
    @JsonProperty("datavctotitulo") val dataVctoTitulo: String,
)

data class DDAAgregadoTO(val ddaagregado: DDAAgregado)

data class DDAAgregado(
    @JsonProperty("inscricaoparceiro") val inscricaoParceiro: String,
    @JsonProperty("tokenusuario") val tokenUsuario: String,
    @JsonProperty("idrequisicao") val idRequisicao: IdRequisicao = IdRequisicao(),
    @JsonProperty("idmodulo") val idModulo: String = "2",
    @JsonProperty("idtransacao") val idTransacao: String = "9",
    @JsonProperty("inscricaotitular") val inscricaotitular: String,
    @JsonProperty("inscricaoagregado") val inscricaoagregado: String,
    @JsonProperty("tipopessoaagregado") val tipopessoaagregado: String = "F",
    @JsonProperty("tipomanutencao") val tipomanutencao: String, // Nesta transação é possível: "A"- Alterar, "C"- Cancelar , "E"- Excluir e "I"- Incluir.
)

data class ArbiResponseTO(
    @JsonProperty("idtransacao") val idTransacao: Int,
    @JsonProperty("resultado") val resultado: String? = null,
    @JsonProperty("idstatus") val idStatus: Int,
    @JsonProperty("idrequisicaoparceiro") val idRequisicaoParceiro: String,
    @JsonProperty("idmodulo") val idModulo: Int,
    @JsonProperty("idrequisicaoarbi") val idRequisicaoArbi: String,
    @JsonProperty("descricaostatus") val descricaoStatus: String,
)

data class BillValidateRequestTO(
    val pagamentos: PagamentosTO,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class PagamentosTO(
    @JsonProperty("inscricaoparceiro") val inscricaoParceiro: String,
    @JsonProperty("tokenusuario") val tokenUsuario: String,
    @JsonProperty("idrequisicao") val idRequisicao: IdRequisicao = IdRequisicao(),
    @JsonProperty("idmodulo") val idModulo: String = "2",
    @JsonProperty("idtransacao") val idTransacao: String = "1",
    @JsonProperty("bancoorigem") val bancoOrigem: String = ARBI_BANK_NO,
    @JsonProperty("agenciaorigem") val agenciaOrigem: String = ARBI_ROUTING_NO,
    @JsonProperty("contaorigem") val contaOrigem: String,
    @JsonProperty("codigobarras") val codigoBarras: String,
    @JsonProperty("valorpagar") val valorPagar: String = "0",
    @JsonProperty("descricao") val descricao: String = "",
    @JsonProperty("complemento") val complemento: String = "",
    @JsonProperty("datapgto") val dataPgto: String = getLocalDate().format(DateTimeFormatter.ISO_DATE),
)

internal data class DomainTO(
    @JsonProperty("iddominio") val iddominio: Int,
    @JsonProperty("descricaodominio") val descricaodominio: String?,
    @JsonProperty("codigodominio") val codigodominio: String,
)

data class CheckingRequestTO(
    @JsonProperty("contacorrente") val contaCorrente: ContaCorrenteTO,
)

data class DdaResultTO(
    @JsonProperty("status") val status: String,
    @JsonProperty("cpfCnpj") val cpfCnpj: String,
    @JsonProperty("descricao") val descricao: String,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class ContaCorrenteTO(
    @JsonProperty("inscricaoparceiro") val inscricaoParceiro: String,
    @JsonProperty("tokenusuario") val tokenUsuario: String,
    @JsonProperty("idrequisicao") val idRequisicao: IdRequisicao = IdRequisicao(),
    @JsonProperty("idmodulo") val idModulo: String,
    @JsonProperty("idtransacao") val idTransacao: String,
    @JsonProperty("bancoorigem") val bancoOrigem: String = ARBI_BANK_NO,
    @JsonProperty("agenciaorigem") val agenciaOrigem: String = ARBI_ROUTING_NO,
    @JsonProperty("contaorigem") val contaOrigem: String,
    @JsonProperty("tipocontadebitada") val tipoContaDebitada: String = "CC",
    @JsonProperty("bancodestino") val bancoDestino: String = "",
    @JsonProperty("agenciadestino") val agenciaDestino: String = "",
    @JsonProperty("contadestino") val contaDestino: String = "",
    @JsonProperty("tipocontacreditada") val tipoContaCreditada: String = "",
    @JsonProperty("cnpjcpfclicred") val cnpjCpfCliCred: String = "",
    @JsonProperty("nomeclicred") val nomeCliCred: String = "",
    @JsonProperty("tipopessoaclicred") val tipoPessoaCliCred: String = "",
    @JsonProperty("finalidade") val finalidade: String = "",
    @JsonProperty("historico") val historico: String = "",
    @JsonProperty("dataagendamento") val dataAgendamento: String,
    @JsonProperty("valor") val valor: String = "0",
    @JsonProperty("datainicial") val dataInicial: String = "",
    @JsonProperty("datafinal") val dataFinal: String = "",
    @JsonProperty("periodoemdias") val periodoEmDias: String = "",
    @JsonProperty("canalentrada") val canalEntrada: String = "E",
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ArbiTransferResponseTO(
    val nroMovDeb: String,
    val nroMovCred: String,
)

internal fun List<DomainTO>.mapUntilDate(limitDate: LocalDate) =
    this.map { LocalDate.parse(it.codigodominio, arbiDateFormat) }
        .takeWhile { it.isBefore(limitDate) || it.isEqual(limitDate) }

data class IdRequisicao @JsonCreator constructor(@JsonValue val value: String = UUID.randomUUID().toString()) {
    init {
        if (value.length > 50) {
            throw IllegalArgumentException()
        }
    }
}

data class ArbiZipCodeRegisterResponseTO(
    @JsonProperty("cep") val cep: String,
    @JsonProperty("rua") val rua: String,
    @JsonProperty("complemento") val complemento: String,
    @JsonProperty("bairro") val bairro: String,
    @JsonProperty("cidade") val cidade: String,
    @JsonProperty("estadoUf") val estado: String,
    @JsonProperty("codigoIbge") val codigoIbge: Int,
)

data class ArbiCalulatorResponseTO(
    @JsonProperty("dataoperacao") val operationDate: String,
    @JsonProperty("idtransacao") val transactionId: Int,
    @JsonProperty("idrequisicaoparceiro") val idRequisicaoParceiro: String,
    @JsonProperty("idmodulo") val idModulo: Int,
    @JsonProperty("valortitulooriginal") val originalAmount: String,
    @JsonProperty("valortotalcobrar") val totalCalculatedAmount: String,
    @JsonProperty("valormultacalculado") val calculatedFineAmount: String,
    @JsonProperty("idstatus") val responseCode: Int,
    @JsonProperty("valorabatimento") val valorAbatimento: String,
    @JsonProperty("valorjuroscalculado") val calculatedInterestAmount: String,
    @JsonProperty("idrequisicaoarbi") val idRequisicaoArbi: String,
    @JsonProperty("descricaostatus") val statusDescription: String,
    @JsonProperty("valordescontocalculado") val calculatedDiscountAmount: String,
)

data class BillAmountCalculationRequest(
    val amountCalculationModel: AmountCalculationModel,
    val originalAmount: Double,
    val abatement: Double,
    val dueDate: LocalDate,
    val interest: AmountCalculationData,
    val fine: AmountCalculationData,
    val discount1: AmountCalculationData,
    val discount2: AmountCalculationData?,
    val discount3: AmountCalculationData?,
)

data class AmountCalculationData(
    val type: String,
    val date: LocalDate?,
    val value: Double?,
)

data class BillAmountCalculationResponse(
    val calculatedAmount: Long,
    val calculatedFinesAmount: Long,
    val calculatedInterestAmount: Long,
    val calculatedDiscountAmount: Long,
)

fun formatDocumentSacadorAvalista(document: String?, type: String?): String? {
    if (document.isNullOrEmpty()) {
        return document
    }
    return when (type) {
        "1" -> document.takeLast(11).padStart(11, '0')
        "2" -> document.takeLast(14).padStart(14, '0')
        else -> ""
    }
}

fun formatDocument(document: String?, type: String?): String? {
    if (document.isNullOrEmpty()) {
        return document
    }
    return when (type) {
        "F" -> document.padStart(11, '0')
        "J" -> document.padStart(14, '0')
        else -> ""
    }
}

internal fun getRecipientChain(response: MutableMap<String, String>) = RecipientChain(
    sacadorAvalista = response["Nom_RzSocSacdrAvalst"]?.takeIf { it.isNotEmpty() }?.let {
        Recipient(
            name = it,
            document = formatDocumentSacadorAvalista(response["IdentcSacdrAvalst"], response["TpIdentcSacdrAvalst"]),
        )
    },
    originalBeneficiary = firstNotNullAndNotEmpty(
        response["NomFantsBenfcrioOr"],
        response["Nom_RzSocBenfcrioOr"],
    )?.let {
        Recipient(
            name = it,
            document = formatDocument(response["CNPJ_CPFBenfcrioOr"], response["TpPessoaBenfcrioOr"]),
        )
    },
    finalBeneficiary = firstNotNullAndNotEmpty(
        response["NomFantsBenfcrioFinl"],
        response["Nom_RzSocBenfcrioFinl"],
    )?.let {
        Recipient(
            name = it,
            document = formatDocument(response["CNPJ_CPFBenfcrioFinl"], response["TpPessoaBenfcrioFinl"]),
        )
    },
)

/**
 * NOTE: Caso uma TEF retorne HTTP status 500, a checagem no extrato via transação 5 não é confiável.
 * Para contornar esses casos, o extrato legado do Arbi (transação 4) foi alterado para trazer o id da operação.
 *
 * O Arbi começou a retornar no campo `finalidade` o número da conta-corrente da contraparte
 * concatenado ao UUID do `OperationId` (somente os 36 últimos dígitos) que enviamos para eles.
 *
 * O motivo é que essa consulta é em um sistema legado, e o campo `finalidade` tem um limite de 50 caracteres.
 * Desta forma, eles vão usar o campo como:
 *  - 10 caracteres inicias para o número da conta (é uma TEF e contas Arbi sempre tem esse tamanho)
 *  - Seguidos dos 36 caracteres do UUID do `OperationId`
 */
sealed class TefFinalidadeParseResult {
    data object Invalid : TefFinalidadeParseResult()
    data class Valid(
        val counterpartAccountNo: String,
        val idRequisicaoParceiroOriginal: String?,
    ) : TefFinalidadeParseResult()

    companion object {
        fun create(value: String): TefFinalidadeParseResult {
            val parts = value.trim().split(" ")
            if (parts.size >= 2) {
                return Valid(
                    counterpartAccountNo = parts[1],
                    idRequisicaoParceiroOriginal = null,
                )
            }

            if (parts.size == 1 && parts[0].length == 46) {
                val counterpartAccountNo = value.take(10)
                val idRequisicaoParceiroOriginal = value.takeLast(36)

                if (Regex("\\d+").matches(counterpartAccountNo)) {
                    return Valid(
                        counterpartAccountNo = counterpartAccountNo,
                        idRequisicaoParceiroOriginal = "${BankOperationId.PREFIX}$idRequisicaoParceiroOriginal",
                    )
                }
            }

            return Invalid
        }
    }
}

fun isTemporaryOperationNumber(operationNumber: String): Boolean =
    operationNumber.length <= 7