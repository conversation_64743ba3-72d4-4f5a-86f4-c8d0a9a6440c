package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.banking.ResolveStatementItemTemporaryNumberTO
import ai.friday.morning.log.andAppend
import ai.friday.morning.messaging.MessageHandler
import ai.friday.morning.messaging.MessageHandlerResponse
import ai.friday.morning.messaging.receiveCount
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

@Handler
@Singleton
open class SQSResolveStatementItemTemporaryOperationNumberHandler(
    private val internalBankService: InternalBankService,
    @Property(name = "friday.morning.messaging.consumer.resolve-statement-item-temporary-operation-number.maxReceiveCount") private val maxRetries: Int,
) : MessageHandler {

    override val configurationName = "resolve-statement-item-temporary-operation-number"

    private val logger = LoggerFactory.getLogger(this::class.java)
    private val logName = "SQSResolveStatementItemTemporaryOperationNumberHandler"

    override fun handleMessage(m: Message): MessageHandlerResponse {
        val marker = Markers.append("body", m)
        val message = parseObjectFrom<ResolveStatementItemTemporaryNumberTO>(m.body())
        marker.andAppend("temporaryOperationNumber", message.temporaryOperationNumber)

        val shouldDelete = internalBankService.resolveTemporaryOperationNumber(
            bankNo = message.bankNo,
            routingNo = message.routingNo,
            accountNumber = AccountNumber(message.fullAccountNumber),
            temporaryOperationNumber = message.temporaryOperationNumber,
        )

        val receiveCount = m.receiveCount()
        marker.andAppend("shouldDelete", shouldDelete)
            .andAppend("receiveCount", receiveCount)

        if (!shouldDelete && receiveCount >= maxRetries) {
            marker.andAppend("maxRetriesExceed", true)
            logger.error(marker, logName)
            return MessageHandlerResponse.delete()
        }
        logger.info(marker, logName)
        return MessageHandlerResponse.build(shouldDelete)
    }

    override fun handleException(
        e: Exception,
    ): MessageHandlerResponse {
        logger.error(Markers.empty(), logName, e)
        return MessageHandlerResponse.keep()
    }
}