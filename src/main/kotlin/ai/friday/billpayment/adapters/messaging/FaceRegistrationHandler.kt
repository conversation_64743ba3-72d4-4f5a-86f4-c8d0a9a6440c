package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.caf.CafService
import ai.friday.billpayment.app.caf.FaceMatchError
import ai.friday.billpayment.app.caf.FaceRegistrationRequest
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requirements(Requires(notEnv = ["test"]))
open class FaceRegistrationHandler(
    sqsClient: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val cafService: CafService,
) : AbstractSQSHandler(
    sqsClient,
    configuration = configuration,
    queueName = configuration.faceRegistrationQueueName,
) {
    private val logger = LoggerFactory.getLogger(FaceRegistrationHandler::class.java)

    override fun handleMessage(message: Message): SQSHandlerResponse {
        try {
            val request = parseObjectFrom<FaceRegistrationRequest>(message.body())
            val markers = message.markers().andAppend("personId", request.personId)

            cafService.registerFace(AccountId(request.personId), request.imageUrl).getOrElse {
                when (it) {
                    is FaceMatchError.TransactionProcessing -> {
                        logger.warn(markers.andAppend("reason", it), "FaceRegistrationHandler#handleMessage")
                        return SQSHandlerResponse(false)
                    }

                    is FaceMatchError.TransactionNotFound, is FaceMatchError.Error -> {
                        logger.error(markers.andAppend("reason", it), "FaceRegistrationHandler#handleMessage")
                        return SQSHandlerResponse(true)
                    }
                }
            }

            logger.info(markers, "FaceRegistrationHandler#handleMessage")
            return SQSHandlerResponse(true)
        } catch (e: Exception) {
            return handleError(message, e)
        }
    }

    override fun handleError(message: Message, e: Exception): SQSHandlerResponse {
        val markers = message.markers()
        logger.error(markers, "FaceRegistrationHandler#handleError", e)
        return SQSHandlerResponse(false)
    }
}