package ai.friday.billpayment.adapters.dynamodb

import io.micronaut.context.annotation.Property
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient

private const val DYNAMODB_BILL_PAYMENT_TABLE_NAME = "dynamodb.billPaymentTableName"
private const val DYNAMODB_BILL_EVENT_TABLE_NAME = "dynamodb.billEventsTableName"

abstract class AbstractBillEventDynamoDAO<T>(
    cli: DynamoDbEnhancedClient,
    type: Class<T>,
) : AbstractDynamoDAO<T>(
    cli = cli,
    type = type,
) {
    private val logger = LoggerFactory.getLogger(AbstractBillEventDynamoDAO::class.java)

    @field:Property(name = DYNAMODB_BILL_EVENT_TABLE_NAME)
    private var tName: String? = null

    override val tableName by lazy {
        logger.info("AbstractBillEventDynamoDAO#tableName: $tName")
        tName ?: BILL_EVENT_TABLE_NAME
    }
}

abstract class AbstractBillPaymentDynamoDAO<T>(
    cli: DynamoDbEnhancedClient,
    type: Class<T>,
) : AbstractDynamoDAO<T>(
    cli = cli,
    type = type,
) {
    private val logger = LoggerFactory.getLogger(AbstractBillPaymentDynamoDAO::class.java)

    @field:Property(name = DYNAMODB_BILL_PAYMENT_TABLE_NAME)
    private var tName: String? = null

    override val tableName by lazy {
        logger.info("AbstractBillPaymentDynamoDAO#tableName: $tName")
        tName ?: BILL_PAYMENT_TABLE_NAME
    }
}

abstract class AbstractBillPaymentDynamoDAOAsync<T>(
    cli: DynamoDbEnhancedAsyncClient,
    type: Class<T>,
) : AbstractAsyncDynamoDAO<T>(
    cli = cli,
    type = type,
) {
    private val logger = LoggerFactory.getLogger(AbstractBillPaymentDynamoDAOAsync::class.java)

    @field:Property(name = DYNAMODB_BILL_PAYMENT_TABLE_NAME)
    private var tName: String? = null

    override val tableName by lazy {
        logger.info("AbstractBillPaymentDynamoDAOAsync#tableName: $tName")
        tName ?: BILL_PAYMENT_TABLE_NAME
    }
}