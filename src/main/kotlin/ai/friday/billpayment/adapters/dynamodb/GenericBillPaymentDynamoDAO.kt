package ai.friday.billpayment.adapters.dynamodb

import io.micronaut.context.annotation.Bean
import io.micronaut.context.annotation.Factory
import jakarta.inject.Named
import jakarta.inject.Singleton
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient

/**
 * Factory para criar instâncias genéricas de BillPaymentDynamoDAO sem precisar criar classes específicas
 * para cada entidade. Usa qualifiers do Micronaut para distinguir entre diferentes tipos de DAO.
 */
@Factory
class BillPaymentDynamoDAOFactory {

    @Bean
    @Singleton
    @Named("accountDAO")
    fun createAccountDAO(cli: DynamoDbEnhancedClient): BillPaymentDynamoDAO<AccountEntity> {
        return BillPaymentDynamoDAO(cli, AccountEntity::class.java)
    }

    @Bean
    @Singleton
    @Named("partialAccountDAO")
    fun createPartialAccountDAO(cli: DynamoDbEnhancedClient): BillPaymentDynamoDAO<PartialAccountEntity> {
        return BillPaymentDynamoDAO(cli, PartialAccountEntity::class.java)
    }

    @Bean
    @Singleton
    @Named("paymentMethodDAO")
    fun createPaymentMethodDAO(cli: DynamoDbEnhancedClient): BillPaymentDynamoDAO<PaymentMethodEntity> {
        return BillPaymentDynamoDAO(cli, PaymentMethodEntity::class.java)
    }

    @Bean
    @Singleton
    @Named("nsuDAO")
    fun createNSUDAO(cli: DynamoDbEnhancedClient): BillPaymentDynamoDAO<NSUEntity> {
        return BillPaymentDynamoDAO(cli, NSUEntity::class.java)
    }

    @Bean
    @Singleton
    @Named("accountRegisterDAO")
    fun createAccountRegisterDAO(cli: DynamoDbEnhancedClient): BillPaymentDynamoDAO<AccountRegisterEntity> {
        return BillPaymentDynamoDAO(cli, AccountRegisterEntity::class.java)
    }

    @Bean
    @Singleton
    @Named("originalOcrAndPersonDataDAO")
    fun createOriginalOcrAndPersonDataDAO(cli: DynamoDbEnhancedClient): BillPaymentDynamoDAO<OriginalOcrAndPersonDataEntity> {
        return BillPaymentDynamoDAO(cli, OriginalOcrAndPersonDataEntity::class.java)
    }
}

/**
 * Classe genérica que substitui as múltiplas classes DAO específicas.
 * Não precisa mais ser abstrata e pode ser injetada diretamente com o tipo de entidade apropriado.
 */
class BillPaymentDynamoDAO<T>(
    cli: DynamoDbEnhancedClient,
    type: Class<T>
) : AbstractBillPaymentDynamoDAO<T>(cli, type)

/**
 * Serviço utilitário para criar DAOs dinamicamente quando necessário.
 * Útil para casos onde você precisa criar um DAO em tempo de execução.
 */
@Singleton
class DynamoDAOProvider(private val cli: DynamoDbEnhancedClient) {

    /**
     * Cria um DAO genérico para qualquer tipo de entidade.
     * Exemplo de uso:
     * val myDAO = dynamoDAOProvider.createDAO<MyEntity>(MyEntity::class.java)
     */
    fun <T> createDAO(entityClass: Class<T>): BillPaymentDynamoDAO<T> {
        return BillPaymentDynamoDAO(cli, entityClass)
    }

    /**
     * Versão inline para Kotlin que permite usar reified types.
     * Exemplo de uso:
     * val myDAO = dynamoDAOProvider.createDAO<MyEntity>()
     */
    inline fun <reified T> createDAO(): BillPaymentDynamoDAO<T> {
        return BillPaymentDynamoDAO(cli, T::class.java)
    }
}
