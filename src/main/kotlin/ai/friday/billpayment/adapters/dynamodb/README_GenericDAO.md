# Generic DynamoDB DAO Solution

## Problema Original

Antes da implementação desta solução, era necessário criar uma nova classe DAO específica para cada entidade:

```kotlin
@Singleton
class AccountDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<AccountEntity>(cli, AccountEntity::class.java)

@Singleton
class PartialAccountDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<PartialAccountEntity>(cli, PartialAccountEntity::class.java)

@Singleton
class PaymentMethodDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<PaymentMethodEntity>(cli, PaymentMethodEntity::class.java)
```

Isso resultava em muito código repetitivo e a necessidade de criar uma nova classe para cada nova entidade.

## Solução Implementada

### 1. Factory Pattern com Qualifiers

A solução usa uma factory (`BillPaymentDynamoDAOFactory`) que cria instâncias genéricas de DAO usando qualifiers do Micronaut para distinguir entre diferentes tipos:

```kotlin
@Factory
class BillPaymentDynamoDAOFactory {
    
    @Bean
    @Singleton
    @Named("accountDAO")
    fun createAccountDAO(cli: DynamoDbEnhancedClient): BillPaymentDynamoDAO<AccountEntity> {
        return BillPaymentDynamoDAO(cli, AccountEntity::class.java)
    }
    
    @Bean
    @Singleton
    @Named("paymentMethodDAO")
    fun createPaymentMethodDAO(cli: DynamoDbEnhancedClient): BillPaymentDynamoDAO<PaymentMethodEntity> {
        return BillPaymentDynamoDAO(cli, PaymentMethodEntity::class.java)
    }
}
```

### 2. Classe DAO Genérica

Uma única classe genérica substitui todas as classes específicas:

```kotlin
class BillPaymentDynamoDAO<T>(
    cli: DynamoDbEnhancedClient,
    type: Class<T>
) : AbstractBillPaymentDynamoDAO<T>(cli, type)
```

### 3. Injeção de Dependência

Os repositórios agora injetam o DAO genérico usando qualifiers:

```kotlin
@Singleton
class AccountDbRepository(
    @Named("accountDAO") private val accountDAO: BillPaymentDynamoDAO<AccountEntity>,
    @Named("partialAccountDAO") private val partialAccountDAO: BillPaymentDynamoDAO<PartialAccountEntity>,
    @Named("paymentMethodDAO") private val paymentMethodDAO: BillPaymentDynamoDAO<PaymentMethodEntity>,
    @Named("nsuDAO") private val nsuDAO: BillPaymentDynamoDAO<NSUEntity>,
    private val transactionDynamo: TransactionDynamo,
) : AccountRepository, BackOfficeAccountRepository
```

## Como Adicionar uma Nova Entidade

### Opção 1: Usando a Factory (Recomendado para DAOs que serão injetados)

1. Adicione um novo método na `BillPaymentDynamoDAOFactory`:

```kotlin
@Bean
@Singleton
@Named("myNewEntityDAO")
fun createMyNewEntityDAO(cli: DynamoDbEnhancedClient): BillPaymentDynamoDAO<MyNewEntity> {
    return BillPaymentDynamoDAO(cli, MyNewEntity::class.java)
}
```

2. Injete no seu repositório:

```kotlin
@Singleton
class MyRepository(
    @Named("myNewEntityDAO") private val myNewEntityDAO: BillPaymentDynamoDAO<MyNewEntity>
) {
    // Usar myNewEntityDAO normalmente
}
```

### Opção 2: Usando o DynamoDAOProvider (Para criação dinâmica)

Para casos onde você precisa criar um DAO em tempo de execução:

```kotlin
@Singleton
class MyService(private val dynamoDAOProvider: DynamoDAOProvider) {
    
    fun someMethod() {
        // Criação dinâmica usando reified types
        val myDAO = dynamoDAOProvider.createDAO<MyNewEntity>()
        
        // Ou usando Class
        val myDAO2 = dynamoDAOProvider.createDAO(MyNewEntity::class.java)
        
        // Usar o DAO normalmente
        myDAO.save(myEntity)
    }
}
```

## Vantagens da Solução

1. **Menos Código Repetitivo**: Não precisa mais criar classes DAO específicas
2. **Flexibilidade**: Pode criar DAOs dinamicamente quando necessário
3. **Manutenibilidade**: Mudanças na lógica base afetam todos os DAOs automaticamente
4. **Type Safety**: Mantém a segurança de tipos do Kotlin/Java
5. **Compatibilidade**: Funciona perfeitamente com as anotações do Micronaut (@Singleton, @Named)

## Migração de Código Existente

Para migrar código existente:

1. Remova as classes DAO específicas (ex: `AccountDynamoDAO`)
2. Adicione os métodos correspondentes na factory
3. Atualize as injeções de dependência para usar `@Named` qualifiers
4. O resto do código permanece inalterado

## Exemplo Completo

```kotlin
// Nova entidade
@DynamoDbBean
class CustomerEntity {
    @get:DynamoDbPartitionKey
    lateinit var id: String
    
    @get:DynamoDbSortKey  
    lateinit var name: String
}

// Adicionar na factory
@Bean
@Singleton
@Named("customerDAO")
fun createCustomerDAO(cli: DynamoDbEnhancedClient): BillPaymentDynamoDAO<CustomerEntity> {
    return BillPaymentDynamoDAO(cli, CustomerEntity::class.java)
}

// Usar no repositório
@Singleton
class CustomerRepository(
    @Named("customerDAO") private val customerDAO: BillPaymentDynamoDAO<CustomerEntity>
) {
    fun save(customer: CustomerEntity) = customerDAO.save(customer)
    fun findById(id: String) = customerDAO.findByPrimaryKey(id, "")
}
```
