micronaut:
  http:
    services:
      arbi:
        url: https://gapp.bancoarbi.com.br
        read-timeout: 60s
        pool:
          max-connections: 30
          enabled: true
        ssl:
          enabled: true
          isInsecureTrustAllCertificates: true
      celcoin:
        url: ${configuration.production.celcoin.url}
        read-timeout: 30s
        pool:
          max-connections: 30
          enabled: true
        ssl:
          enabled: true
          client-authentication: NEED
          key-store:
            path: classpath:ssl/celcoin-mtls.p12
            type: PKCS12
            password: FROM_SECRETS
      settlement-service:
        url: https://liquidacao.friday.ai
        read-timeout: 20s
        pool:
          max-connections: 30
          enabled: true
      vehicle-debts-service:
        url: https://debitos-veiculares.friday.ai
        read-timeout: 120s
        pool:
          max-connections: 30
          enabled: true
  session:
    http:
      redis:
        value-serializer: io.micronaut.jackson.serialize.JacksonObjectSerializer
  security:
    token:
      jwt:
        signatures:
          jwks:
            awscognito:
              url: 'https://cognito-idp.${application.region}.amazonaws.com/${integrations.cognito.userPoolId}/.well-known/jwks.json'
            google:
              url: 'https://www.googleapis.com/oauth2/v3/certs'
            apple:
              url: 'https://appleid.apple.com/auth/keys'
  netty:
    event-loops:
      other:
        num-threads: 50
  executors:
    scheduled:
      type: scheduled
      core-pool-size: 200
    io:
      type: fixed
      nThreads: 500

modules:
  manual-entry:
    enabled: true
  pfm:
    enabled: true
  chatbot-ai:
    enabled: true
  event-api:
    enabled: true
  openfinance:
    enabled: true
  push-notification:
    enabled: true
  in-app-subscription-coupon:
    enabled: true

jwtValidation:
  providers:
    google:
      audience:
        - ***********-lvk3pfvtsqjhb75lesb974niah1pmdhs.apps.googleusercontent.com
        - ***********-eu6be7n1th80mdfq6ir802bpl4ngapkk.apps.googleusercontent.com
      issuer: "https://accounts.google.com"
    apple:
      audience:
        - app.via1
        - app.via1.webapp
      issuer: "https://appleid.apple.com"
    msisdn:
      audience:
        - ${msisdn-authentication.jwt.audience}
      issuer: ${msisdn-authentication.jwt.issuer}


features:
  createTopic: true
  vehicleDebts: true
  inAppSubscription: 1
  userPilotEnabled: true
  silenceNotifications: false
  updateScheduleOnAmountLowered: true
  updateAmountAfterPaymentWindow: true
  filterAccountsThatReceiveNotification: true
  credit-card-risk-analisys:
    provider: "clearsale"
  imageReceipt: true

concessionariaDireto:
  financialGateway: CELCOIN

integrations:
  intercom:
    tenant: "Friday"
  softwareExpress:
    url: https://esitef.softwareexpress.com.br
  arbi:
    newAuthHost: https://gapp.bancoarbi.com.br
    newHost: https://gapp.bancoarbi.com.br
    pixCheckoutPoolingInterval: 2500 #miliseconds
    fepweb:
      host: https://bancoarbi.fepweb.com.br
  openfinance:
    host: "https://open-finance.friday.ai"
    clientid: OPEN_FINANCE_CLIENT_ID-ed2f3006-74c2-47c6-b9ed-8024badc0144
    clientsecret: OPEN_FINANCE_pwd_prod_!@#_to_be_replaced

  vehicle-debts:
    tenant: FRIDAY

internalBankService:
  omnibusBankAccount:
    accountNo: 326400

createBillService:
  idSubscriptionBy:
    recipientDocument: **************

subscription:
  amount: 990
  dayOfMonth: 10
  description: "%s"
  recipientName: Assinatura Friday
  recipientDocument: **************
  bankAccount:
    accountType: CHECKING
    bankNo: 213
    routingNo: 1
    accountNo: 320074
    accountDv: 1
    ispb: ********

bill-tracking:
  doNotTrackAfterDays: 120

redis:
  uri: redis://bill-payment-cache.orwoqd.0001.use1.cache.amazonaws.com:6379
  cache:
    value-serializer: io.micronaut.jackson.serialize.JacksonObjectSerializer
  caches:
    balance:
      charset: 'UTF-8'
      expire-after-write: 2m
      expire-after-access: 2m
    find-pix-key-details:
      charset: 'UTF-8'
      expire-after-write: 5m
      expire-after-access: 5m

accountRegister:
  user_files:
    modattaUserDocumentsBucket: ************-modatta-user-documents

basic-register:
  phones:
    - "+*************"

export-s3:
  bucket: friday-dynamodb-exports
  prefix: "billpayment"
  tableArn: "arn:aws:dynamodb:us-east-1:************:table/Via1-BillPayment"

export-events-s3:
  bucket: friday-dynamodb-exports
  prefix: "userevents"
  tableArn: "arn:aws:dynamodb:us-east-1:************:table/Via1-UserEvents"

export-openfinancedata-s3:
  bucket: friday-dynamodb-exports
  prefix: "openfinancedata"
  tableArn: "arn:aws:dynamodb:us-east-1:************:table/Friday-OpenFinanceData"

kms:
  openfinance:
    keyid: 46769f9d-d5fe-49d5-9e05-b371399ee37c