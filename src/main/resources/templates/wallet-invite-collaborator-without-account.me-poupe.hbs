<!DOCTYPE html>
<html lang="pt-BR" style="box-sizing: border-box; cursor: default; line-height: 1.5; -moz-tab-size: 4; tab-size: 4; -webkit-tap-highlight-color: transparent; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; word-break: break-word;">
<head>
    <title>Me Poupe</title>
    <meta charset="utf-8">
    <meta name="color-scheme" content="only">
    <meta name="supported-color-schemes" content="only">
    <style type="text/css">
/* Document
 * ========================================================================== */
    *,
    ::before,
    ::after { box-sizing: border-box; }

    ::before,
    ::after { text-decoration: inherit; vertical-align: inherit; }

    html {
        cursor: default;
        line-height: 1.5;
        -moz-tab-size: 4;
        tab-size: 4;
        -webkit-tap-highlight-color: transparent;
        -ms-text-size-adjust: 100%;
        -webkit-text-size-adjust: 100%;
        word-break: break-word;
    }

    body { margin: 0; }

    h1 { font-size: 2em; margin: 0.67em 0; }

    dl dl, dl ol, dl ul, ol dl, ul dl { margin: 0; }
    ol ol, ol ul, ul ol, ul ul { margin: 0; }

    hr { height: 0; overflow: visible; }
    main { display: block; }

    nav ol, nav ul { list-style: none; padding: 0; }

    pre { font-family: monospace, monospace; font-size: 1em; }

    abbr[title] { text-decoration: underline; text-decoration: underline dotted; }

    b, strong { font-weight: bolder; }

    code, kbd, samp { font-family: monospace, monospace; font-size: 1em; }

    small { font-size: 80%; }

    audio, canvas, iframe, img, svg, video { vertical-align: middle; }
    iframe { border-style: none; }
    svg:not([fill]) { fill: currentColor; }
    svg:not(:root) { overflow: hidden; }

    table { border-collapse: collapse; }

    button, input, select { margin: 0; }
    button { overflow: visible; text-transform: none; }
    button, [type="button"], [type="reset"], [type="submit"] { -webkit-appearance: button; }

    fieldset { border: 1px solid #a0a0a0; padding: 0.35em 0.75em 0.625em; }

    input { overflow: visible; }

    legend { color: inherit; display: table; max-width: 100%; white-space: normal; }

    progress { display: inline-block; vertical-align: baseline; }

    select { text-transform: none; }

    textarea { margin: 0; overflow: auto; resize: vertical; }

    [type="search"] { -webkit-appearance: textfield; outline-offset: -2px; }

    ::-webkit-inner-spin-button, ::-webkit-outer-spin-button { height: auto; }

    ::-webkit-input-placeholder { color: inherit; opacity: 0.54; }

    ::-webkit-search-decoration { -webkit-appearance: none; }

    ::-webkit-file-upload-button { -webkit-appearance: button; font: inherit; }

    ::-moz-focus-inner { border-style: none; padding: 0; }

    :-moz-focusring { outline: 1px dotted ButtonText; }

    :-moz-ui-invalid { box-shadow: none; }

    details { display: block; }

    dialog {
        background-color: white; border: solid; color: black; display: block;
        height: -moz-fit-content; height: -webkit-fit-content; height: fit-content;
        left: 0; margin: auto; padding: 1em; position: absolute; right: 0;
        width: -moz-fit-content; width: -webkit-fit-content; width: fit-content;
    }

    dialog:not([open]) { display: none; }

    summary { display: list-item; }

    template { display: none; }

    a, area, button, input, label, select, summary, textarea, [tabindex] {
        -ms-touch-action: manipulation; touch-action: manipulation;
    }

    [aria-busy="true"] { cursor: progress; }

    [aria-controls] { cursor: pointer; }

    [aria-disabled="true"], [disabled] { cursor: not-allowed; }

    [aria-hidden="false"][hidden] { display: initial; }
    [aria-hidden="false"][hidden]:not(:focus) { clip: rect(0, 0, 0, 0); position: absolute; }

    body { background-color: #f5f6f7; color: #444a4b; }

    #base-layout {
        font-size: 16px;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
        -webkit-font-smoothing: antialiased;
        padding: 30px 0; background-color: #f5f6f7;
    }

    #base-layout > .main, #base-layout > .footer { max-width: 600px; margin: 0 auto; }

    #base-layout > .main { background-color: #fff; border-radius: 10px; }

    #base-layout > .footer {
        padding: 30px 40px; font-size: 13px; text-align: center; color: #7a869a;
    }

    #base-layout > .footer .links { margin-top: 20px; }

    #base-layout > .footer .links a + a { margin-left: 12px; }

    #base-layout > .footer p { text-align: center; max-width: 300px; margin-left: auto; margin-right: auto; }

    #base-layout h1, #base-layout h2, #base-layout h3, #base-layout h4, #base-layout li, #base-layout p { margin: 0; }

    #base-layout p { text-align: left; line-height: 1.5; color: #444a4b; }

    #base-layout p + p, #base-layout p + a, #base-layout p + div { margin-top: 16px; }

    #base-layout a { color: #444a4b; }

    #base-layout .action {
        display: block; padding: 20px; margin: 30px 0; text-align: center; font-size: 18px; text-decoration: none;
        font-weight: 800; border-radius: 10px; background-color: #ff5757; color: #fff; box-shadow: 0 13px 10px -10px rgba(239, 102, 6, .5);
    }

    #message-layout { padding: 30px 40px; }

    #message-layout > .header { margin-bottom: 30px; }

    #message-layout > .signature { margin-top: 30px; }

    #list li { margin: 10px 0; }
    </style>
</head>
<body style="box-sizing: border-box; margin: 0; background-color: #f5f6f7; color: #444a4b;">
<div id="base-layout" style="box-sizing: border-box; font-size: 16px; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Cantarell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif; -webkit-font-smoothing: antialiased; padding: 30px 0; background-color: #f5f6f7;">
    <div class="main" style="box-sizing: border-box; max-width: 600px; margin: 0 auto; background-color: #fff; border-radius: 10px;">
        <div id="message-layout" style="box-sizing: border-box; padding: 30px 40px;">
            <div class="header" style="box-sizing: border-box; margin-bottom: 30px;">
                <img src="https://notification-templates-cdn.mepoupe.app/static/logo-purple.d72e31c09b38ae46fb2772999c3febbb.png" alt="Me Poupe" style="box-sizing: border-box; vertical-align: middle;">
            </div>

            <div class="content" style="box-sizing: border-box;">
                <p style="box-sizing: border-box; margin: 0; text-align: left; line-height: 1.5; color: #444a4b;">Olá, a Me Poupe te dá as boas-vindas!</p>
                <p style="box-sizing: border-box; margin: 0; text-align: left; line-height: 1.5; color: #444a4b; margin-top: 16px;">
                    <strong style="box-sizing: border-box; font-weight: bolder;">{{founderName}}</strong> está te convidando para compartilhar a sua carteira na Me Poupe e simplificar a rotina de organização e pagamento de despesas de vocês.
                </p>

                <p style="box-sizing: border-box; margin: 0; text-align: left; line-height: 1.5; color: #444a4b; margin-top: 16px;">
                    <strong style="box-sizing: border-box; font-weight: bolder;">Juntos, vocês organizam, controlam e pagam todas as despesas em um único app:</strong> todos os boletos, transferências, contas de consumo, entre outros. E o melhor, pagando em poucos cliques.
                </p>

                <p style="box-sizing: border-box; margin: 0; text-align: left; line-height: 1.5; color: #444a4b; margin-top: 16px;">O jeito mais fácil de pagar contas é fácil, é Me Poupe!</p>

                <p style="box-sizing: border-box; margin: 0; text-align: left; line-height: 1.5; color: #444a4b; margin-top: 16px;">Vamos começar?</p>

                <ul id="list" style="box-sizing: border-box;">
                    <li style="box-sizing: border-box; margin: 10px 0;">Tenha sua CNH ou RG física ou em um arquivo digital;</li>
                    <li style="box-sizing: border-box; margin: 10px 0;">Baixe o aplicativo da Me Poupe <a href="https://mepoupe.go.link?adj_t=1e6wq9bm" style="box-sizing: border-box; -ms-touch-action: manipulation; touch-action: manipulation; color: #444a4b;">clicando aqui</a>;</li>
                    <li style="box-sizing: border-box; margin: 10px 0;">Faça seu cadastro, aguarde a aprovação e então aceite o convite dentro do App.</li>
                </ul>

                <p style="box-sizing: border-box; margin: 0; text-align: left; line-height: 1.5; color: #444a4b;">Ficou com alguma dúvida? <a href="https://wa.me/+551140200153" style="box-sizing: border-box; -ms-touch-action: manipulation; touch-action: manipulation; color: #444a4b;">Clique aqui</a> e fale com a gente</p>
            </div>

            <div class="signature" style="box-sizing: border-box; margin-top: 30px;">
                <p style="box-sizing: border-box; margin: 0; text-align: left; line-height: 1.5; color: #444a4b;">Um abraço,</p>
                <p style="box-sizing: border-box; margin: 0; text-align: left; line-height: 1.5; color: #444a4b; margin-top: 16px;">Equipe Me Poupe</p>
            </div>
        </div>
    </div>

    <div class="footer" style="box-sizing: border-box; max-width: 600px; margin: 0 auto; padding: 30px 40px; font-size: 13px; text-align: center; color: #7a869a;">
        <p style="box-sizing: border-box; margin: 0; line-height: 1.5; color: #444a4b; text-align: center; max-width: 300px; margin-left: auto; margin-right: auto;">Me Poupe © 2024 Todos os direitos reservados.</p>
        <p style="box-sizing: border-box; margin: 0; line-height: 1.5; color: #444a4b; margin-top: 16px; text-align: center; max-width: 300px; margin-left: auto; margin-right: auto;">Me Poupe Pagamentos Digitais Ltda. está inscrita no CNPJ 56.152.016/0001-74.</p>

        <div class="links" style="box-sizing: border-box; margin-top: 20px;">
            <a href="https://use.mepoupe.app" target="_blank" rel="noreferrer noopener" style="box-sizing: border-box; -ms-touch-action: manipulation; touch-action: manipulation; color: #444a4b;">Acessar sua conta</a>
            <a href="https://me-poupe-contas-bill-payment-public-production.s3.amazonaws.com/current/Me_Poupe_Politica_de_Privacidade.pdf" target="_blank" rel="noreferrer noopener" style="box-sizing: border-box; -ms-touch-action: manipulation; touch-action: manipulation; color: #444a4b; margin-left: 12px;">Política de privacidade</a>
        </div>
    </div>
</div>
</body>
</html>