<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <title>GigU Conta - Declaração de residência</title>
    <meta charset="utf-8"/>
    <meta name="color-scheme" content="only"/>
    <meta name="supported-color-schemes" content="only"/>
    <style type="text/css">body {
        padding: 0;
        margin: 0;
        background-color: #ffffff;
        color: #444a4b
    }

    #base-layout {
        font-size: 16px;
        font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
        -webkit-font-smoothing: antialiased;
        padding: 2cm 1cm
    }

    #base-layout header {
        display: block;
        overflow: hidden;
        margin-bottom: 40px
    }

    #base-layout header img {
        height: 60px;
        float: left
    }

    #base-layout header h1 {
        float: right;
        font-size: 20px;
        font-weight: 800;
        color: #444a4b;
        text-transform: uppercase;
        margin-top: 20px
    }

    #base-layout section {
        display: block
    }

    #base-layout section + section {
        margin-top: 20px
    }

    #base-layout address {
        display: block;
        font-weight: 600
    }

    #base-layout .quote {
        margin-left: 10px;
        padding-left: 30px;
        font-style: italic;
        border-left: 4px solid #687189
    }

    #base-layout .signature {
        display: inline-block;
        border: 3px solid #687189;
        background-color: #f6f6f6;
        border-radius: 10px;
        padding: 15px;
        margin-top: 30px;
        overflow: hidden
    }

    #base-layout .signature h2 {
        font-size: 16px;
        margin-bottom: 10px
    }

    #base-layout .signature p {
        font-style: italic;
        white-space: nowrap
    }

    #base-layout .signature span {
        display: inline-block;
        font-style: normal;
        font-weight: 500;
        font-size: 14px;
        min-width: 110px
    }

    #base-layout h1, #base-layout h2, #base-layout h3, #base-layout h4, #base-layout li, #base-layout p {
        margin: 0
    }

    #base-layout p {
        text-align: justify;
        line-height: 1.5;
        color: #444a4b
    }

    </style>
</head>

<body style="padding: 0; margin: 0; background-color: #ffffff; color: #444a4b;">
<div id="base-layout"
     style="font-size: 16px; font-family: 'Poppins',-apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Cantarell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif; -webkit-font-smoothing: antialiased; padding: 2cm 1cm;">
    <header style="display: block; overflow: hidden; margin-bottom: 40px;">
        <img src="https://notification-templates-cdn.via1.app/static/gigu-conta-logo.png"
             alt="GigU" style="height: 60px; float: left;" height="60"/>
        <h1 style="margin: 0; float: right; font-size: 20px; font-weight: 800; color: #444a4b; text-transform: uppercase; margin-top: 20px;">
            Declaração de residência</h1>
    </header>
    <section style="display: block;">
        <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b;">
            Eu, <strong>{{fullName}}, inscrito no CPF sob o n.º {{document}}</strong>, declaro para os devidos fins e
            sob as penas da legislação brasileira em vigor, com a finalidade de realizar prova de residência junto ao
            <strong>BANCO ARBI S/A, inscrito no CNPJ sob o n.º 54.403.563/0001-50</strong>, ser residente e domiciliado
            no endereço abaixo:
        </p>
    </section>
    <section style="display: block; margin-top: 20px;">
        <address style="display: block; font-weight: 600;">
            <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b;">{{fullAddress}}</p>
            <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b;">{{zipCode}} - {{city}}
                / {{federalUnity}}</p>
        </address>
    </section>
    <section style="display: block; margin-top: 20px;">
        <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b;">
            Em conformidade ao disposto nos artigos 2º e 3º da Lei 7.115/1983, declaro ainda, estar ciente de que a
            ausência de autenticidade das informações ora prestadas poderá ensejar a aplicação de sanções cíveis,
            administrativas e penais prevista na legislação brasileira em vigor, inclusive aquelas referentes a
            falsidade prevista no Código Penal Brasileiro:
        </p>
    </section>
    <section class="quote"
             style="display: block; margin-top: 20px; margin-left: 10px; padding-left: 30px; font-style: italic; border-left: 4px solid #687189;">
        <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b;">
            Art. 299: Omitir, em documento público ou particular, declaração que nele deveria constar, ou nele inserir
            ou fazer inserir declaração falsa ou diversa da que devia ser escrita, com o fim de prejudicar direito,
            criar obrigação ou alterar a verdade sobre o fato juridicamente relevante, pena de reclusão de 1 (um) a 5
            (cinco) anos e multa, se o documento é público e reclusão de 1 (um) a 3 (três) anos, se o documento é
            particular.
        </p>
    </section>
    <section style="display: block; margin-top: 20px;">
        <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b;">
            Por ser verdade e de minha responsabilidade todas as declarações ora prestadas, dato e assino a presente
            declaração.
        </p>
    </section>
    <section style="display: block; margin-top: 20px;">
        <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b;">{{city}}, {{signature.date}}.</p>

        <div class="signature"
             style="display: inline-block; border: 3px solid #687189; background-color: #f6f6f6; border-radius: 10px; padding: 15px; margin-top: 30px; overflow: hidden;">
            <h2 style="margin: 0; font-size: 16px; margin-bottom: 10px;">Assinado digitalmente
                em {{signature.shortDate}} por:</h2>
            <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b; font-style: italic; white-space: nowrap;">
                <span style="display: inline-block; font-style: normal; font-weight: 500; font-size: 14px; min-width: 110px;">Nome:</span> {{fullName}}
            </p>
            <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b; font-style: italic; white-space: nowrap;">
                <span style="display: inline-block; font-style: normal; font-weight: 500; font-size: 14px; min-width: 110px;">Email:</span> {{signature.email}}
            </p>
            <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b; font-style: italic; white-space: nowrap;">
                <span style="display: inline-block; font-style: normal; font-weight: 500; font-size: 14px; min-width: 110px;">Telefone:</span> {{signature.phone}}
            </p>
            <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b; font-style: italic; white-space: nowrap;">
                <span style="display: inline-block; font-style: normal; font-weight: 500; font-size: 14px; min-width: 110px;">CPF:</span> {{document}}
            </p>
            <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b; font-style: italic; white-space: nowrap;">
                <span style="display: inline-block; font-style: normal; font-weight: 500; font-size: 14px; min-width: 110px;">Endereço IP:</span> {{signature.clientIP}}
            </p>
            <p style="margin: 0; text-align: justify; line-height: 1.5; color: #444a4b; font-style: italic; white-space: nowrap;">
                <span style="display: inline-block; font-style: normal; font-weight: 500; font-size: 14px; min-width: 110px;">Autenticação:</span> {{signature.key}}
            </p>
        </div>
    </section>
</div>
</body>

</html>