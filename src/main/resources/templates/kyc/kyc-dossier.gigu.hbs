<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <title>GigU Conta - Dossiê KYC</title>
    <meta charset="utf-8"/>
    <meta name="color-scheme" content="only"/>
    <meta name="supported-color-schemes" content="only"/>

    <style type="text/css">
        .data-row.warning .data-value {
            color: red !important;
        }

        .data-value li.warning {
            color: red !important;
        }
    </style>
<style type="text/css">@page{size:A4 portrait;margin:100px 40px;@top-left{content:element(header-left);border-bottom:2px solid #dfdfdf;display:block}@top-center{border-bottom:2px solid #dfdfdf;display:block}@top-right{content:element(header-right);border-bottom:2px solid #dfdfdf;display:block}@bottom-center{content:"GigU Conta Pagamentos Digitais inscrita no XX.XXX.XXX/0001-XX";font-size:14px;text-align:center;color:#7a869a}@bottom-right{content:counter(page) "/" counter(pages);font-size:14px;color:#7a869a}}body{background-color:#fff;color:#444a4b;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Oxygen","Ubuntu","Cantarell","Fira Sans","Droid Sans","Helvetica Neue",sans-serif;font-size:16px;-webkit-font-smoothing:antialiased}.header-left{position:running(header-left)}.header-left h1{font-size:26px;margin-bottom:6px;color:#626262;font-weight:500}.header-left h2{padding-left:2px;font-size:14px;font-weight:400;color:#626262;margin-bottom:0}.header-left h2 strong{font-weight:500}.header-right{position:running(header-right);text-align:right}.header-right img{display:inline-block}.main{background-color:#fff;padding-top:20px}.main table{page-break-inside:avoid;-fs-table-paginate:paginate;width:100%;border-collapse:collapse;margin-top:20px}.main table:first-child{margin-top:0}.main table h3{margin-bottom:18px}.main table tr{margin:0}.main table tr+tr{border-top:0}.main table td{padding:6px;border:1px solid #9b9b9b}.main table td:first-child{width:160px;background-color:#f3f3f3}.main .data-entry{padding-left:10px}.main .data-entry h4,.main .data-entry h5{color:#454953}.main .data-entry h4+table,.main .data-entry h5+table{margin-top:12px}.main .data-entry h4+h5{font-weight:400;margin-top:8px;margin-bottom:0px}.main .data-entry+.data-entry{margin-top:24px}.main .data-entry .data-row-result .data-value{color:green}.main .data-row{line-height:1}.main .data-row .data-label{font-size:14px;font-weight:500;color:#5b6377}.main .data-row .data-label small{font-size:10px;font-weight:400;text-transform:uppercase;color:#838ca3}.main .data-row .data-value{font-family:monospace,"Courier New"}.main .data-row .data-value small{font-size:12px;color:#687189}.main .data-row .data-value ul{font-size:14px;color:#687189}ul{list-style:none;margin:0;padding:0;list-style:decimal;list-style-position:inside}h1,h2,h3,h4,li,p{margin:0}p{text-align:left;line-height:1.5;color:#1e273e}p+a,p+div{margin-top:16px}a{color:#1e273e}
</style></head>
<body style="background-color: #fff; color: #444a4b; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Cantarell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif; font-size: 16px; -webkit-font-smoothing: antialiased;">
    <div class="header-left" style="position: running(header-left);">
        <h1 style="margin: 0; font-size: 26px; margin-bottom: 6px; color: #626262; font-weight: 500;">Relatório KYC</h1>
        <h2 style="margin: 0; padding-left: 2px; font-size: 14px; font-weight: 400; color: #626262; margin-bottom: 0;"><strong style="font-weight: 500;">Referência:</strong> {{accountId}}</h2>
    </div>
    <div class="header-right" style="position: running(header-right); text-align: right;">
        <img src="https://notification-templates-cdn.via1.app/static/gigu-conta-logo.png" alt="GigU" style="display: inline-block;"/>
    </div>
    <div class="main" style="background-color: #fff; padding-top: 20px;">
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; margin-top: 0; padding-left: 10px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Geral</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Tipo de cadastro</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{registrationType}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Data de criação</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{createdAt}}</td>
            </tr>
            <tr class="data-row {{#if risk.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Classificação de risco</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{risk.result}}</td>
            </tr>
        </table>

        {{#with personalData}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Dados pessoais</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nome completo<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">informado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{fullName.declared}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nome completo<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">consultado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{fullName.query.value}}</td>
            </tr>
            <tr class="data-row {{#if fullName.query.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Coincide com o CPF?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{fullName.query.result}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Data de nascimento<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">informada</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{birthDate.declared}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Data de nascimento<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">consultada</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{birthDate.query.value}}</td>
            </tr>
            <tr class="data-row {{#if birthDate.query.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Coincide com o CPF?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{birthDate.query.result}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Sexo<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">consultado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{gender}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nome da mãe<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">consultado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{motherName.query.value}}</td>
            </tr>
            {{#if motherName.declared}}
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nome da mãe<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">informado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{motherName.declared}} </td>
            </tr>
            <tr class="data-row {{#if motherName.query.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Análise do<br/>nome da mãe</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{motherName.query.result}} </td>
            </tr>
            {{/if}}
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nome do pai<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">consultado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{fatherName.query.value}}</td>
            </tr>
            {{#if fatherName.declared}}
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nome do pai<br/><small style="font-size: 10px; font-weight: 400; text-transform: uppercase; color: #838ca3;">informado</small></td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{fatherName.declared}} </td>
            </tr>
            <tr class="data-row {{#if fatherName.query.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Análise do<br/>nome do pai</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{fatherName.query.result}} </td>
            </tr>
            {{/if}}
        </table>
        {{/with}}

        {{#with biometry}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Biometria</h4></td>
                </tr>
            </thead>
            <tr class="data-row {{#if liveness.warning}}warning{{/if}}" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Prova de vida</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{liveness.result}}</td>
            </tr>
            <tr class="data-row {{#if identity.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Rosto em base<br/>oficial do governo?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    {{identity.result}}<br/>
                    {{#if identity.percentage}}<small style="font-size: 12px; color: #687189;">{{identity.percentage}} de similaridade</small>{{/if}}
                </td>
            </tr>
            <tr class="data-row {{#if duplications.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Rosto já tem<br/>cadastro?</td>
                    <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    {{duplications.result}}<br/>
                    {{#if duplications.accounts}}
                    <small style="font-size: 12px; color: #687189;">
                        {{#each duplications.accounts}}
                        {{#if @last}}{{this}}{{else}}{{this}}<br/>{{/if}}
                        {{/each}}
                    </small>
                    {{/if}}
                </td>
            </tr>
            <tr class="data-row {{#if fraudIndications.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Rosto em lista<br/>de fraudadores?</td>
                    <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    {{fraudIndications.result}}<br/>
                    {{#if fraudIndications.accounts}}
                    <small style="font-size: 12px; color: #687189;">
                        {{#each fraudIndications.accounts}}
                        {{#if @last}}{{this}}{{else}}{{this}}<br/>{{/if}}
                        {{/each}}
                    </small>
                    {{/if}}
                </td>
            </tr>
            {{#if documentFaceMatch}}
            <tr class="data-row {{#if documentFaceMatch.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Similaridade do rosto<br/>com documento enviado</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    {{documentFaceMatch.result}}<br/>
                    {{#if documentFaceMatch.percentage}}<small style="font-size: 12px; color: #687189;">{{documentFaceMatch.percentage}}</small>{{/if}}
                </td>
            </tr>
            {{/if}}
        </table>
        {{/with}}

        {{#with documentScan}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Análise do documento</h4></td>
                </tr>
            </thead>
            <tr class="data-row {{#if digitalSpoof.warning}}warning{{/if}}" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">É um documento<br/>físico?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{digitalSpoof.value}}</td>
            </tr>
            <tr class="data-row {{#if face.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Foto do rosto<br/>adulterada?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{face.value}}</td>
            </tr>
            <tr class="data-row {{#if text.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Dados do documento<br/>adulterados?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{text.value}}</td>
            </tr>
            <tr class="data-row {{#if ocr.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Leitura de dados<br/>automatizada</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    {{ocr.value}}
                    <br/>
                    <small style="font-size: 12px; color: #687189;">Os dados lidos não são exibidos neste documento</small>
                </td>
            </tr>
        </table>
        {{/with}}

        {{#with cpf}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Consulta ao CPF</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">CPF</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{value}}</td>
            </tr>
            <tr class="data-row {{#if status.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Situação</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{status.value}}</td>
            </tr>
            <tr class="data-row {{#if hasObitIndication.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Indicação de óbito</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{hasObitIndication.value}}</td>
            </tr>
            <tr class="data-row {{#if inFraudList.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">CPF em lista<br/>de fraudadores?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{inFraudList.value}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Região</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{region}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Possui alguma sanção?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    {{#if sanctions.length}}
                    <ul style="margin: 0; padding: 0; font-size: 14px; color: #687189; list-style: decimal; list-style-position: inside;">
                        {{#each sanctions}}
                        <li class="{{#if this.warning}}warning{{/if}}" style="margin: 0;">{{this.value}}</li>
                        {{/each}}
                    </ul>
                    {{else}}
                    Nenhuma sanção encontrada
                    {{/if}}
                    <br/>
                    <small style="font-size: 12px; color: #687189;">Lista de sanções analisadas ao final do arquivo</small>
                </td>
            </tr>
        </table>
        {{/with}}

        {{#with email}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">E-mail verificado por código</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">E-mail</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{value}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Tempo de base</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{createdAt}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Recente</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{recent}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Ativo</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{active}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Associado ao CPF?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{relatedToCpf}}</td>
            </tr>
        </table>
        {{/with}}

        {{#with phone}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Telefone verificado por código</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Telefone</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{value}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Tempo de base</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{createdAt}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Recente</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{recent}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Ativo</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{active}}</td>
            </tr>
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Associado ao CPF?</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{relatedToCpf}}</td>
            </tr>
        </table>
        {{/with}}

        {{#with monthlyIncome}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;">Faixa de renda</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Valor autodeclarado</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{value}}<br/><small style="font-size: 12px; color: #687189;">Sem quaisquer verificações adicionais</small></td>
            </tr>
        </table>
        {{/with}}

        {{#with mep}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3">
                        <h4 style="margin: 0; color: #454953;">Exposição e perfil na mídia</h4>
                        <h5 style="color: #454953; font-weight: 400; margin-top: 8px; margin-bottom: 0px;">Classificado de A (mais elevado) a H (menos elevado)</h5>
                    </td>
                </tr>
            </thead>
            <tr class="data-row {{#if exposure.warning}}warning{{/if}}" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Exposição (6 meses)</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{exposure.level}}</td>
            </tr>
            <tr class="data-row {{#if celebrity.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Nível celebridade</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{celebrity.level}}</td>
            </tr>
            <tr class="data-row {{#if unpopularity.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Impopularidade</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{unpopularity.level}}</td>
            </tr>
        </table>
        {{/with}}

        {{#with pep}}
        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3">
                        <h4 style="margin: 0; color: #454953;">Pessoa politicamente exposta</h4>
                    </td>
                </tr>
            </thead>
            <tr class="data-row {{#if selfDeclared.warning}}warning{{/if}}" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Autodeclarado</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{selfDeclared.value}}</td>
            </tr>
            <tr class="data-row {{#if query.warning}}warning{{/if}}" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Consultado</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{query.value}}</td>
            </tr>
            {{#if query.level}}
            <tr class="data-row" style="margin: 0; border-top: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Exposição</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">{{query.level}}<br/><small style="font-size: 12px; color: #687189;">Nível 1: pessoas que estão listadas diretamente<br/>Nível 2:
                    pessoas que tem algum relacionamento com PPE</small></td>
            </tr>
            {{/if}}
        </table>
        {{/with}}

        <table class="data-entry" style="page-break-inside: avoid; -fs-table-paginate: paginate; width: 100%; border-collapse: collapse; padding-left: 10px; margin-top: 24px;" width="100%">
            <thead>
                <tr style="margin: 0;">
                    <td colspan="2" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3;" width="160" bgcolor="#f3f3f3"><h4 style="margin: 0; color: #454953;"> Lista de sanções analisadas</h4></td>
                </tr>
            </thead>
            <tr class="data-row" style="margin: 0; line-height: 1;">
                <td class="data-label" style="padding: 6px; border: 1px solid #9b9b9b; width: 160px; background-color: #f3f3f3; font-size: 14px; font-weight: 500; color: #5b6377;" width="160" bgcolor="#f3f3f3">Consultado</td>
                <td class="data-value" style="padding: 6px; border: 1px solid #9b9b9b; font-family: monospace,'Courier New';">
                    <ul style="list-style: none; margin: 0; padding: 0; list-style: decimal; list-style-position: inside; font-size: 14px; color: #687189;">
                    {{#each globalSanctionsList}}
                        <li style="margin: 0;">{{this}}</li>
                    {{/each}}
                    </ul>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>